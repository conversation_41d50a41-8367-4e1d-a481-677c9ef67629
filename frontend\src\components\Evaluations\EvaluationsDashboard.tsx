import { useState, useMemo } from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts';
import { Expand, EyeOff } from 'lucide-react';
import { useDashboardChartsData } from '../../hooks/useEvaluations';
import { useRealtimeEvaluationsSubscription } from '../../hooks/useRealtimeEvaluations';
import { UnifiedFilters } from './UnifiedFilters';

export const EvaluationsDashboard = () => {
  // Estados para filtros unificados
  const [filters, setFilters] = useState({
    estado: [] as string[],
    workflow_ids: [] as string[],
    workflow_tags: [] as string[],
    solo_con_sugerencias: false,
    puntuacion_min: undefined as number | undefined,
    puntuacion_max: undefined as number | undefined,
    fecha_desde: undefined as string | undefined,
    fecha_hasta: undefined as string | undefined
  });

  const [expandedChart, setExpandedChart] = useState<string | null>(null);

  // Obtener datos del dashboard
  const { data: dashboardData, isLoading, error } = useDashboardChartsData();

  // Activar suscripciones en tiempo real
  useRealtimeEvaluationsSubscription();

  // Funciones para manejar filtros
  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleClearFilters = () => {
    setFilters({
      estado: [] as string[],
      workflow_ids: [] as string[],
      workflow_tags: [] as string[],
      solo_con_sugerencias: false,
      puntuacion_min: undefined as number | undefined,
      puntuacion_max: undefined as number | undefined,
      fecha_desde: undefined as string | undefined,
      fecha_hasta: undefined as string | undefined
    });
  };

  // Filtrar workflows según selección
  const filteredWorkflows = useMemo(() => {
    if (!dashboardData?.workflows) return [];

    return dashboardData.workflows.filter((workflow: any) => {
      // Filtrar por workflows seleccionados
      if (filters.workflow_ids.length > 0 && !filters.workflow_ids.includes(workflow.n8n_workflow_id)) {
        return false;
      }

      // Filtrar por etiquetas si están seleccionadas
      if (filters.workflow_tags.length > 0) {
        const workflowTags = workflow.etiquetas || [];
        const hasMatchingTag = filters.workflow_tags.some((tag: string) =>
          workflowTags.includes(tag)
        );
        if (!hasMatchingTag) return false;
      }

      return true;
    }).map((workflow: any) => ({
      ...workflow,
      agentes: workflow.agentes.filter((agente: any) => {
        // Filtrar evaluaciones del agente por fecha
        const evaluacionesFiltradas = agente.evaluaciones.filter((evaluacion: any) => {
          const fechaEvaluacion = new Date(evaluacion.created_at);

          if (filters.fecha_desde) {
            const fechaDesde = new Date(filters.fecha_desde);
            if (fechaEvaluacion < fechaDesde) return false;
          }

          if (filters.fecha_hasta) {
            const fechaHasta = new Date(filters.fecha_hasta);
            fechaHasta.setHours(23, 59, 59, 999); // Incluir todo el día
            if (fechaEvaluacion > fechaHasta) return false;
          }

          // Filtrar por puntuación
          if (filters.puntuacion_min !== undefined && evaluacion.puntuacion < filters.puntuacion_min) {
            return false;
          }

          if (filters.puntuacion_max !== undefined && evaluacion.puntuacion > filters.puntuacion_max) {
            return false;
          }

          return true;
        });

        return {
          ...agente,
          evaluaciones: evaluacionesFiltradas
        };
      }).filter((agente: any) => agente.evaluaciones.length > 0)
    })).filter((workflow: any) => workflow.agentes.length > 0);
  }, [dashboardData, filters]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Cargando datos del dashboard...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center">
          <div className="text-red-600 font-medium">Error cargando datos</div>
        </div>
        <div className="text-red-600 text-sm mt-1">
          {error instanceof Error ? error.message : 'Error desconocido'}
        </div>
      </div>
    );
  }

  if (!dashboardData?.workflows || dashboardData.workflows.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 text-lg mb-2">No hay datos disponibles</div>
        <div className="text-gray-400 text-sm">
          No se encontraron evaluaciones para mostrar en el dashboard.
        </div>
      </div>
    );
  }



  // Componente para gráfico individual de agente
  const AgentChart = ({ agente, isExpanded = false }: { agente: any, workflowId?: string, isExpanded?: boolean }) => {
    const chartData = useMemo(() => {
      const sortedData = agente.evaluaciones
        .map((evaluacion: any) => ({
          fecha: new Date(evaluacion.created_at).toLocaleDateString('es-ES', {
            day: '2-digit',
            month: '2-digit',
            year: '2-digit'
          }),
          fechaCompleta: new Date(evaluacion.created_at).toLocaleDateString('es-ES', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          }),
          puntuacion: evaluacion.puntuacion,
          timestamp: new Date(evaluacion.created_at).getTime(),
          created_at: evaluacion.created_at,
          index: 0 // Se asignará después del ordenamiento
        }))
        .sort((a: any, b: any) => a.timestamp - b.timestamp)
        .map((item: any, index: number) => ({ ...item, index: index + 1 }));

      // Si hay muchos datos y no está expandido, reducir la densidad
      if (!isExpanded && sortedData.length > 50) {
        const step = Math.ceil(sortedData.length / 50);
        return sortedData.filter((_: any, index: number) => index % step === 0);
      }

      return sortedData;
    }, [agente.evaluaciones, isExpanded]);

    const mejoras = useMemo(() => {
      return agente.mejoras_aplicadas.map((mejora: any) => ({
        fecha: new Date(mejora.fecha).toLocaleDateString('es-ES', {
          day: '2-digit',
          month: '2-digit',
          year: '2-digit'
        }),
        timestamp: new Date(mejora.fecha).getTime()
      }));
    }, [agente.mejoras_aplicadas]);

    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">
            {agente.nombre_agente_amigable || agente.nombre_agente_workflow}
          </h3>
          <button
            onClick={() => setExpandedChart(agente.agente_key)}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
            title="Ampliar gráfico"
          >
            <Expand className="h-4 w-4" />
          </button>
        </div>

        <div className={isExpanded ? "h-96" : "h-64"}>
          {isExpanded && chartData.length > 100 ? (
            // Vista expandida con scroll horizontal para muchos datos
            <div className="w-full h-full overflow-x-auto">
              <div style={{ width: Math.max(800, chartData.length * 15), height: '100%' }}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={chartData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 60 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis
                      dataKey="fecha"
                      tick={{ fontSize: 10 }}
                      angle={-45}
                      textAnchor="end"
                      height={60}
                      interval={0}
                    />
                    <YAxis
                      domain={[0, 100]}
                      tick={{ fontSize: 11 }}
                      label={{ value: 'Puntuación (%)', angle: -90, position: 'insideLeft' }}
                    />
                    <Tooltip
                      labelFormatter={(label, payload) => {
                        if (payload && payload[0]) {
                          return `Fecha: ${(payload[0] as any).payload.fechaCompleta}`;
                        }
                        return `Fecha: ${label}`;
                      }}
                      formatter={(value: any, _name: any, props: any) => [
                        `${value}%`,
                        `Puntuación (Evaluación #${props.payload?.index || 1})`
                      ]}
                      contentStyle={{
                        backgroundColor: 'white',
                        border: '1px solid #e5e7eb',
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                      }}
                    />
                    <Line
                      type="monotone"
                      dataKey="puntuacion"
                      stroke="#2563eb"
                      strokeWidth={2}
                      dot={{ fill: '#2563eb', strokeWidth: 2, r: 3 }}
                      activeDot={{ r: 5, fill: '#1d4ed8' }}
                    />
                    {/* Marcadores de mejoras aplicadas */}
                    {mejoras.map((mejora: any, index: number) => (
                      <ReferenceLine
                        key={index}
                        x={mejora.fecha}
                        stroke="#ef4444"
                        strokeWidth={2}
                        strokeDasharray="5 5"
                        label={{
                          value: "Mejora",
                          position: "top",
                          style: { fontSize: '10px', fill: '#ef4444' }
                        }}
                      />
                    ))}
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          ) : (
            // Vista normal/compacta
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={chartData}
                margin={{ top: 5, right: 30, left: 20, bottom: isExpanded ? 60 : 20 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                {isExpanded ? (
                  <XAxis
                    dataKey="fecha"
                    tick={{ fontSize: 11 }}
                    angle={-45}
                    textAnchor="end"
                    height={60}
                    interval="preserveStartEnd"
                  />
                ) : (
                  <XAxis
                    dataKey="index"
                    tick={{ fontSize: 10 }}
                    label={{ value: 'Evaluaciones', position: 'insideBottom', offset: -5 }}
                    interval="preserveStartEnd"
                  />
                )}
                <YAxis
                  domain={[0, 100]}
                  tick={{ fontSize: 11 }}
                  label={{ value: 'Puntuación (%)', angle: -90, position: 'insideLeft' }}
                />
                <Tooltip
                  labelFormatter={(label, payload) => {
                    if (payload && payload[0]) {
                      const data = (payload[0] as any).payload;
                      return isExpanded
                        ? `Fecha: ${data.fechaCompleta}`
                        : `Evaluación #${data.index} - ${data.fechaCompleta}`;
                    }
                    return isExpanded ? `Fecha: ${label}` : `Evaluación #${label}`;
                  }}
                  formatter={(value: any) => [
                    `${value}%`,
                    'Puntuación'
                  ]}
                  contentStyle={{
                    backgroundColor: 'white',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="puntuacion"
                  stroke="#2563eb"
                  strokeWidth={2}
                  dot={{
                    fill: '#2563eb',
                    strokeWidth: 2,
                    r: chartData.length > 50 ? 2 : 4
                  }}
                  activeDot={{ r: 6, fill: '#1d4ed8' }}
                />
                {/* Marcadores de mejoras aplicadas - solo en vista expandida */}
                {isExpanded && mejoras.map((mejora: any, index: number) => (
                  <ReferenceLine
                    key={index}
                    x={mejora.fecha}
                    stroke="#ef4444"
                    strokeWidth={2}
                    strokeDasharray="5 5"
                    label={{
                      value: "Mejora",
                      position: "top",
                      style: { fontSize: '10px', fill: '#ef4444' }
                    }}
                  />
                ))}
              </LineChart>
            </ResponsiveContainer>
          )}
        </div>

        <div className="mt-3 flex items-center justify-between text-sm text-gray-600">
          <div className="flex flex-col">
            <span>Evaluaciones: {agente.evaluaciones.length}</span>
            {!isExpanded && agente.evaluaciones.length > 50 && (
              <span className="text-xs text-blue-600">
                (Mostrando {chartData.length} de {agente.evaluaciones.length})
              </span>
            )}
          </div>
          <div className="flex flex-col items-end">
            {agente.mejoras_aplicadas.length > 0 && (
              <span className="text-red-600">
                Mejoras aplicadas: {agente.mejoras_aplicadas.length}
              </span>
            )}
            {agente.evaluaciones.length > 0 && (
              <span className="text-xs text-gray-500">
                Última: {new Date(agente.evaluaciones[agente.evaluaciones.length - 1]?.created_at).toLocaleDateString('es-ES')}
              </span>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Filtros unificados */}
      <UnifiedFilters
        filters={filters}
        onFilterChange={handleFilterChange}
        onClearFilters={handleClearFilters}
        showEstado={false}
        showWorkflows={true}
        showEtiquetas={true}
        showSugerencias={false}
        showPuntuacion={true}
        showFechas={true}
        showAgente={false}
        estadoType="evaluaciones"
        title="Filtros del Dashboard"
      />

      {/* Estadísticas generales */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="text-3xl font-bold text-blue-600">{dashboardData.total_evaluaciones}</div>
          <div className="text-sm text-gray-600">Total Evaluaciones</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="text-3xl font-bold text-green-600">{dashboardData.total_workflows}</div>
          <div className="text-sm text-gray-600">Workflows</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="text-3xl font-bold text-purple-600">{dashboardData.total_agentes}</div>
          <div className="text-sm text-gray-600">Agentes Únicos</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="text-3xl font-bold text-orange-600">
            {dashboardData.workflows?.reduce((acc: number, w: any) =>
              acc + (w.agentes?.reduce((agentAcc: number, a: any) =>
                agentAcc + (a.mejoras_aplicadas?.length || 0), 0) || 0), 0) || 0}
          </div>
          <div className="text-sm text-gray-600">Mejoras Aplicadas</div>
        </div>
      </div>

      {/* Secciones por Workflow */}
      {filteredWorkflows.length === 0 ? (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
          <EyeOff className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No hay datos para mostrar</h3>
          <p className="text-gray-600">
            {filters.workflow_ids.length > 0 || filters.workflow_tags.length > 0
              ? 'Los filtros aplicados no coinciden con ningún dato disponible.'
              : 'No se encontraron evaluaciones para mostrar en el dashboard.'}
          </p>
        </div>
      ) : (
        filteredWorkflows.map((workflow: any) => (
          <div key={workflow.n8n_workflow_id} className="space-y-4">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg px-6 py-4">
              <h3 className="text-xl font-semibold text-gray-900">
                {workflow.workflow_nombre || `Workflow ${workflow.n8n_workflow_id}`}
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                {workflow.agentes.length} agente{workflow.agentes.length !== 1 ? 's' : ''}
                {workflow.agentes.length > 0 && ` • ${workflow.agentes.reduce((acc: number, a: any) => acc + a.evaluaciones.length, 0)} evaluaciones totales`}
              </p>
            </div>

            {/* Cuadrícula de gráficos de agentes */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {workflow.agentes.map((agente: any) => (
                <AgentChart
                  key={agente.agente_key}
                  agente={agente}
                  workflowId={workflow.n8n_workflow_id}
                  isExpanded={false}
                />
              ))}
            </div>
          </div>
        ))
      )}

      {/* Modal para gráfico ampliado */}
      {expandedChart && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-semibold text-gray-900">
                  Gráfico Ampliado
                </h2>
                <button
                  onClick={() => setExpandedChart(null)}
                  className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Cerrar"
                >
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {(() => {
                const agente = filteredWorkflows
                  .flatMap((w: any) => w.agentes)
                  .find((a: any) => a.agente_key === expandedChart);

                if (!agente) return null;

                return (
                  <div className="h-96">
                    <AgentChart agente={agente} workflowId="" isExpanded={true} />
                  </div>
                );
              })()}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
