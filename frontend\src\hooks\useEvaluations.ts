import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useState, useCallback } from 'react';
import apiClient from '../lib/api';
import {
  EvaluacionCreate,
  EvaluacionUpdate,
  EvaluacionBatchUpdate,
  EstadoEvaluacion,
  EvaluacionSortBy,
  EvaluacionGroupBy,

} from '../types/evaluaciones';

// Query keys
export const evaluacionesKeys = {
  all: ['evaluaciones'] as const,
  lists: () => [...evaluacionesKeys.all, 'list'] as const,
  list: (params: any) => [...evaluacionesKeys.lists(), params] as const,
  details: () => [...evaluacionesKeys.all, 'detail'] as const,
  detail: (id: number) => [...evaluacionesKeys.details(), id] as const,
  stats: () => [...evaluacionesKeys.all, 'stats'] as const,
  filterOptions: () => [...evaluacionesKeys.all, 'filter-options'] as const,
};

// Hook para listar evaluaciones
export const useEvaluaciones = (params?: {
  page?: number;
  page_size?: number;
  estado?: EstadoEvaluacion[];
  workflow_ids?: string[];
  workflow_tags?: string[];
  agente_id?: string[];
  puntuacion_min?: number;
  puntuacion_max?: number;
  fecha_desde?: string;
  fecha_hasta?: string;
  solo_con_sugerencias?: boolean;
  sort_by?: EvaluacionSortBy;
  group_by?: EvaluacionGroupBy;
}) => {
  // Serialize array parameters for API compatibility
  const serializedParams = params ? {
    ...params,
    estado: params.estado && params.estado.length > 0 ? params.estado.join(',') : undefined,
    workflow_ids: params.workflow_ids && params.workflow_ids.length > 0 ? params.workflow_ids.join(',') : undefined,
    workflow_tags: params.workflow_tags && params.workflow_tags.length > 0 ? params.workflow_tags.join(',') : undefined,
    agente_id: params.agente_id && params.agente_id.length > 0 ? params.agente_id.join(',') : undefined,
  } : undefined;

  return useQuery({
    queryKey: evaluacionesKeys.list(params),
    queryFn: () => apiClient.evaluaciones.getAll(serializedParams as any),
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para obtener una evaluación específica
export const useEvaluacion = (id: number | null) => {
  return useQuery({
    queryKey: evaluacionesKeys.detail(id || 0),
    queryFn: () => apiClient.evaluaciones.getById(id!),
    enabled: !!id && id > 0,
  });
};

// Hook para estadísticas de evaluaciones
export const useEvaluacionStats = () => {
  return useQuery({
    queryKey: evaluacionesKeys.stats(),
    queryFn: () => apiClient.evaluaciones.getStats(),
    staleTime: 10 * 60 * 1000, // 10 minutos
  });
};

// Hook para datos de gráficos del dashboard
export const useDashboardChartsData = () => {
  return useQuery({
    queryKey: ['dashboard-charts-data'],
    queryFn: () => apiClient.evaluaciones.getDashboardCharts(),
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para crear evaluación
export const useCreateEvaluacion = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: EvaluacionCreate) => apiClient.evaluaciones.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: evaluacionesKeys.lists() });
      queryClient.invalidateQueries({ queryKey: evaluacionesKeys.stats() });
    },
  });
};

// Hook para actualizar evaluación
export const useUpdateEvaluacion = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: EvaluacionUpdate }) =>
      apiClient.evaluaciones.update(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: evaluacionesKeys.lists() });
      queryClient.invalidateQueries({ queryKey: evaluacionesKeys.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: evaluacionesKeys.stats() });
    },
  });
};

// Hook para eliminar evaluación
export const useDeleteEvaluacion = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => apiClient.evaluaciones.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: evaluacionesKeys.lists() });
      queryClient.invalidateQueries({ queryKey: evaluacionesKeys.stats() });
    },
  });
};

// Hook para actualización en lote
export const useBatchUpdateEvaluaciones = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: EvaluacionBatchUpdate) => apiClient.evaluaciones.batchUpdate(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: evaluacionesKeys.lists() });
      queryClient.invalidateQueries({ queryKey: evaluacionesKeys.stats() });
    },
  });
};

// Hook para marcar como revisado
export const useMarkEvaluacionReviewed = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => apiClient.evaluaciones.markReviewed(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: evaluacionesKeys.lists() });
      queryClient.invalidateQueries({ queryKey: evaluacionesKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: evaluacionesKeys.stats() });
    },
  });
};

// Hook para descartar evaluación
export const useDiscardEvaluacion = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => apiClient.evaluaciones.discard(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: evaluacionesKeys.lists() });
      queryClient.invalidateQueries({ queryKey: evaluacionesKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: evaluacionesKeys.stats() });
    },
  });
};

// Hook personalizado para manejo de filtros y estado local
export const useEvaluacionesWithFilters = (initialFilters?: any) => {
  const [filters, setFilters] = useState({
    page: initialFilters?.page || 1,
    page_size: initialFilters?.page_size || 20,
    estado: initialFilters?.estado || [EstadoEvaluacion.PENDIENTE_REVISION] as EstadoEvaluacion[],
    workflow_ids: initialFilters?.workflow_ids || [] as string[],
    workflow_tags: initialFilters?.workflow_tags || [] as string[],
    agente_id: initialFilters?.agente_id || [] as string[],
    puntuacion_min: initialFilters?.puntuacion_min as number | undefined,
    puntuacion_max: initialFilters?.puntuacion_max as number | undefined,
    fecha_desde: initialFilters?.fecha_desde as string | undefined,
    fecha_hasta: initialFilters?.fecha_hasta as string | undefined,
    solo_con_sugerencias: initialFilters?.solo_con_sugerencias as boolean | undefined,
    sort_by: initialFilters?.sort_by || EvaluacionSortBy.FECHA_DESC,
    group_by: initialFilters?.group_by || EvaluacionGroupBy.NINGUNO,
  });

  const [selectedIds, setSelectedIds] = useState<number[]>([]);

  const evaluacionesQuery = useEvaluaciones(filters);
  const batchUpdateMutation = useBatchUpdateEvaluaciones();

  const updateFilters = useCallback((newFilters: Partial<typeof filters>) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 })); // Reset page when filters change
  }, []);

  const resetFilters = useCallback(() => {
    setFilters({
      page: 1,
      page_size: 20,
      estado: [],
      workflow_ids: [],
      workflow_tags: [],
      agente_id: [],
      puntuacion_min: undefined,
      puntuacion_max: undefined,
      fecha_desde: undefined,
      fecha_hasta: undefined,
      solo_con_sugerencias: undefined,
      sort_by: EvaluacionSortBy.FECHA_DESC,
      group_by: EvaluacionGroupBy.NINGUNO,
    });
    setSelectedIds([]);
  }, []);

  const handleBatchUpdate = useCallback(async (estado: EstadoEvaluacion) => {
    if (selectedIds.length === 0) return;

    await batchUpdateMutation.mutateAsync({
      evaluacion_ids: selectedIds,
      estado,
    });

    setSelectedIds([]);
  }, [selectedIds, batchUpdateMutation]);

  const toggleSelection = useCallback((id: number) => {
    setSelectedIds(prev => 
      prev.includes(id) 
        ? prev.filter(selectedId => selectedId !== id)
        : [...prev, id]
    );
  }, []);

  const selectAll = useCallback(() => {
    if (evaluacionesQuery.data?.evaluaciones) {
      setSelectedIds(evaluacionesQuery.data.evaluaciones.map(e => e.id));
    }
  }, [evaluacionesQuery.data]);

  const clearSelection = useCallback(() => {
    setSelectedIds([]);
  }, []);

  return {
    // Data
    evaluaciones: evaluacionesQuery.data?.evaluaciones || [],
    total: evaluacionesQuery.data?.total || 0,
    totalPages: evaluacionesQuery.data?.total_pages || 0,
    
    // Loading states
    isLoading: evaluacionesQuery.isLoading,
    isError: evaluacionesQuery.isError,
    error: evaluacionesQuery.error,
    isBatchUpdating: batchUpdateMutation.isPending,
    
    // Filters
    filters,
    updateFilters,
    resetFilters,
    
    // Selection
    selectedIds,
    toggleSelection,
    selectAll,
    clearSelection,
    
    // Actions
    handleBatchUpdate,
    
    // Refetch
    refetch: evaluacionesQuery.refetch,
  };
};

// Hook para obtener workflows para filtros
export const useWorkflowsForFilters = () => {
  return useQuery({
    queryKey: ['evaluaciones', 'workflows'],
    queryFn: () => apiClient.evaluaciones.getWorkflows(),
    staleTime: 10 * 60 * 1000, // 10 minutos - las opciones no cambian frecuentemente
  });
};

// Hook para obtener etiquetas para filtros
export const useEtiquetasForFilters = () => {
  return useQuery({
    queryKey: ['evaluaciones', 'etiquetas'],
    queryFn: () => apiClient.evaluaciones.getEtiquetas(),
    staleTime: 10 * 60 * 1000, // 10 minutos - las opciones no cambian frecuentemente
  });
};
