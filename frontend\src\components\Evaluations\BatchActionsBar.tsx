import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Clock, AlertTriangle, X } from 'lucide-react';
import { EstadoEvaluacion } from '../../types/evaluaciones';

interface BatchActionsBarProps {
  selectedCount: number;
  onBatchUpdate: (estado: EstadoEvaluacion) => Promise<void>;
  onClearSelection: () => void;
  isLoading?: boolean;
}

export const BatchActionsBar: React.FC<BatchActionsBarProps> = ({
  selectedCount,
  onBatchUpdate,
  onClearSelection,
  isLoading = false
}) => {
  const [isUpdating, setIsUpdating] = useState(false);

  const handleBatchUpdate = async (estado: EstadoEvaluacion) => {
    if (isUpdating) return;
    
    setIsUpdating(true);
    try {
      await onBatchUpdate(estado);
    } finally {
      setIsUpdating(false);
    }
  };

  if (selectedCount === 0) {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50">
      <div className="bg-white rounded-lg shadow-lg border border-gray-200 px-6 py-4">
        <div className="flex items-center space-x-4">
          {/* Contador de seleccionados */}
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-xs font-medium text-blue-600">{selectedCount}</span>
            </div>
            <span className="text-sm font-medium text-gray-700">
              {selectedCount} evaluación{selectedCount !== 1 ? 'es' : ''} seleccionada{selectedCount !== 1 ? 's' : ''}
            </span>
          </div>

          {/* Separador */}
          <div className="h-6 w-px bg-gray-300"></div>

          {/* Acciones */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => handleBatchUpdate(EstadoEvaluacion.REVISADO)}
              disabled={isUpdating || isLoading}
              className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-green-700 bg-green-50 border border-green-200 rounded-md hover:bg-green-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <CheckCircle className="w-4 h-4" />
              <span>Marcar como Revisado</span>
            </button>

            <button
              onClick={() => handleBatchUpdate(EstadoEvaluacion.PENDIENTE_MEJORAS)}
              disabled={isUpdating || isLoading}
              className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <Clock className="w-4 h-4" />
              <span>Pendiente Mejoras</span>
            </button>
          </div>

          {/* Separador */}
          <div className="h-6 w-px bg-gray-300"></div>

          {/* Botón cerrar */}
          <button
            onClick={onClearSelection}
            disabled={isUpdating || isLoading}
            className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title="Limpiar selección"
          >
            <X className="w-4 h-4" />
          </button>
        </div>

        {/* Indicador de carga */}
        {(isUpdating || isLoading) && (
          <div className="mt-3 flex items-center justify-center">
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span>Actualizando evaluaciones...</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Componente de confirmación para acciones masivas
interface BatchConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  selectedCount: number;
  action: string;
  estado: EstadoEvaluacion;
}

export const BatchConfirmationModal: React.FC<BatchConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  selectedCount,
  action,
  estado
}) => {
  if (!isOpen) return null;

  const getEstadoLabel = (estado: EstadoEvaluacion) => {
    const labels = {
      [EstadoEvaluacion.PENDIENTE_REVISION]: 'Pendiente Revisión',
      [EstadoEvaluacion.REVISADO]: 'Revisado',
      [EstadoEvaluacion.PENDIENTE_MEJORAS]: 'Pendiente Mejoras',
      [EstadoEvaluacion.MEJORAS_APLICADAS]: 'Mejoras Aplicadas'
    };
    return labels[estado];
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-5 h-5 text-yellow-600" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">Confirmar acción masiva</h3>
              <p className="text-sm text-gray-500">Esta acción no se puede deshacer</p>
            </div>
          </div>

          <div className="mb-6">
            <p className="text-sm text-gray-700">
              ¿Estás seguro de que quieres cambiar el estado de{' '}
              <span className="font-medium">{selectedCount} evaluación{selectedCount !== 1 ? 'es' : ''}</span>{' '}
              a <span className="font-medium">{getEstadoLabel(estado)}</span>?
            </p>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 transition-colors"
            >
              Cancelar
            </button>
            <button
              onClick={onConfirm}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 transition-colors"
            >
              Confirmar {action}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
