from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import SecretStr
from functools import lru_cache
from typing import Optional

class Settings(BaseSettings):
    """
    Application settings loaded from environment variables.
    Uses pydantic-settings for automatic loading and validation.
    """
    # Supabase configuration
    SUPABASE_URL: str = "YOUR_SUPABASE_URL"
    SUPABASE_ANON_KEY: SecretStr = SecretStr("YOUR_SUPABASE_ANON_KEY")
    SUPABASE_SERVICE_ROLE_KEY: SecretStr = SecretStr("YOUR_SUPABASE_SERVICE_ROLE_KEY")
    SUPABASE_JWT_SECRET: SecretStr = SecretStr("YOUR_SUPABASE_JWT_SECRET") # Found in Supabase Project Settings -> API -> JWT Settings
    SUPABASE_AUDIENCE: str = "authenticated" # Default Supabase audience

    # OpenAI configuration
    OPENAI_API_KEY: SecretStr = SecretStr("YOUR_OPENAI_API_KEY")

    # Gemini configuration (fallback for transcription)
    GEMINI_API_KEY: SecretStr = SecretStr("YOUR_GEMINI_API_KEY")
    GEMINI_MODEL: str = "gemini-2.5-flash-preview-05-20"

    # Database direct connection configuration (for asyncpg pools)
    DB_HOST: str = "YOUR_DB_HOST"
    DB_PORT: int = 5432
    DB_NAME: str = "YOUR_DB_NAME"
    DB_USER: str = "YOUR_DB_USER"
    DB_PASSWORD: SecretStr = SecretStr("YOUR_DB_PASSWORD")
    DB_REPLICA_HOST: Optional[str] = None  # For read-only replica, if available

    # n8n Webhook configuration
    N8N_WEBHOOK_URL_CHAT: str = "YOUR_N8N_WEBHOOK_URL" # Renamed - Example: https://automatizaciones.aceleralia.com/webhook/f6b61028-13a3-461d-b070-c3871983bc2f
    N8N_WEBHOOK_URL_ENVIARAGOOGLEDOCS: str = "YOUR_N8N_SEND_TO_DOCS_WEBHOOK_URL" # Webhook for sending content to Google Docs
    N8N_WEBHOOK_URL_REUNION_TRANSCRIPCION: str = "YOUR_N8N_REUNION_TRANSCRIPTION_WEBHOOK_URL" # For Meeting Processing - Transcription
    N8N_WEBHOOK_URL_REUNION_ANALYSIS: str = "YOUR_N8N_REUNION_ANALYSIS_WEBHOOK_URL" # For Meeting Processing - AI Analysis
    N8N_WEBHOOK_APLICAR_MEJORAS_PROMPT: str = "YOUR_N8N_APLICAR_MEJORAS_WEBHOOK_URL" # For applying prompt improvements

    N8N_API_KEY: SecretStr = SecretStr("YOUR_N8N_API_KEY_FOR_SQL_TOOL") # API Key for securing the SQL execution tool endpoint

    # JWT Configuration (Optional: if needed beyond Supabase verification)
    # SECRET_KEY: str = "YOUR_SECRET_KEY"
    # ALGORITHM: str = "HS256"
    # ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # Define the location of the .env file
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")

@lru_cache()
def get_settings() -> Settings:
    """
    Returns the settings instance, cached for performance.
    """
    return Settings()

# Initialize settings instance for easy import elsewhere if needed
settings = get_settings()