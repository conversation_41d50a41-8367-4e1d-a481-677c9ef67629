from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional
from uuid import UUID

from app.core.security import get_current_user_payload
from typing import Dict, Any
from app.models.mejora_agente import (
    MejoraAgente,
    MejoraAgenteCreate,
    MejoraAgenteUpdate,
    MejoraAgenteDetail,
    MejoraAgenteListResponse,
    MejoraAgenteFilters,
    MejoraAgenteSortBy,
    MejoraAgenteApplyRequest,
    MejoraAgenteApplyResponse,
    PromptComparison,
    MejoraAgenteStats,
    EstadoMejoraAgente
)
from app.services.mejora_agente_service import MejoraAgenteService

router = APIRouter()

@router.post("/", response_model=MejoraAgente)
async def create_mejora_agente(
    mejora_data: MejoraAgenteCreate,
    current_user: Dict[str, Any] = Depends(get_current_user_payload)
):
    """Crear una nueva mejora de agente."""
    try:
        service = MejoraAgenteService()
        return await service.create_mejora_agente(mejora_data, current_user["sub"])
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/", response_model=MejoraAgenteListResponse)
async def list_mejoras_agentes(
    page: int = Query(1, ge=1, description="Número de página"),
    page_size: int = Query(20, ge=1, le=100, description="Tamaño de página"),
    # Filtros
    estado: Optional[List[EstadoMejoraAgente]] = Query(None, description="Filtrar por estado"),
    agente_id: Optional[List[UUID]] = Query(None, description="Filtrar por ID de agente"),
    n8n_workflow_id: Optional[List[str]] = Query(None, description="Filtrar por workflow ID"),
    fecha_desde: Optional[str] = Query(None, description="Fecha desde (ISO format)"),
    fecha_hasta: Optional[str] = Query(None, description="Fecha hasta (ISO format)"),
    # Ordenamiento
    sort_by: MejoraAgenteSortBy = Query(MejoraAgenteSortBy.FECHA_CREACION_DESC, description="Ordenar por"),
    current_user: Dict[str, Any] = Depends(get_current_user_payload)
):
    """Listar mejoras de agente con filtros, ordenamiento y paginación."""
    try:
        # Construir filtros
        filters = None
        if any([estado, agente_id, n8n_workflow_id, fecha_desde, fecha_hasta]):
            from datetime import datetime
            filters = MejoraAgenteFilters(
                estado=estado,
                agente_id=agente_id,
                n8n_workflow_id=n8n_workflow_id,
                fecha_desde=datetime.fromisoformat(fecha_desde) if fecha_desde else None,
                fecha_hasta=datetime.fromisoformat(fecha_hasta) if fecha_hasta else None
            )
        
        service = MejoraAgenteService()
        return await service.list_mejoras_agentes(
            current_user["sub"], page, page_size, filters, sort_by
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/{mejora_id}", response_model=MejoraAgenteDetail)
async def get_mejora_agente(
    mejora_id: UUID,
    current_user: Dict[str, Any] = Depends(get_current_user_payload)
):
    """Obtener una mejora de agente por ID con detalles completos."""
    try:
        service = MejoraAgenteService()
        mejora = await service.get_mejora_agente_by_id(mejora_id, current_user["sub"])
        if not mejora:
            raise HTTPException(status_code=404, detail="Mejora de agente no encontrada")
        return mejora
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/{mejora_id}", response_model=MejoraAgente)
async def update_mejora_agente(
    mejora_id: UUID,
    mejora_data: MejoraAgenteUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user_payload)
):
    """Actualizar una mejora de agente."""
    try:
        service = MejoraAgenteService()
        mejora = await service.update_mejora_agente(mejora_id, mejora_data, current_user["sub"])
        if not mejora:
            raise HTTPException(status_code=404, detail="Mejora de agente no encontrada")
        return mejora
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/{mejora_id}")
async def delete_mejora_agente(
    mejora_id: UUID,
    current_user: Dict[str, Any] = Depends(get_current_user_payload)
):
    """Eliminar una mejora de agente."""
    try:
        service = MejoraAgenteService()
        success = await service.delete_mejora_agente(mejora_id, current_user["sub"])
        if not success:
            raise HTTPException(status_code=404, detail="Mejora de agente no encontrada")
        return {"message": "Mejora de agente eliminada exitosamente"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/{mejora_id}/apply", response_model=MejoraAgenteApplyResponse)
async def apply_mejora_agente(
    mejora_id: UUID,
    current_user: Dict[str, Any] = Depends(get_current_user_payload)
):
    """Aplicar una mejora de agente vía webhook n8n."""
    service = MejoraAgenteService()
    apply_request = MejoraAgenteApplyRequest(mejora_id=mejora_id)
    result = await service.apply_mejora_agente(apply_request, current_user["sub"])

    # El servicio ya maneja los errores y devuelve success=False, no necesitamos lanzar excepción
    return result

@router.post("/{mejora_id}/discard")
async def discard_mejora_agente(
    mejora_id: UUID,
    current_user: Dict[str, Any] = Depends(get_current_user_payload)
):
    """Descartar una mejora de agente."""
    try:
        service = MejoraAgenteService()
        mejora = await service.update_mejora_agente(
            mejora_id,
            MejoraAgenteUpdate(estado=EstadoMejoraAgente.DESCARTADO),
            current_user["sub"]
        )
        if not mejora:
            raise HTTPException(status_code=404, detail="Mejora de agente no encontrada")
        return {"message": "Mejora de agente descartada", "mejora": mejora}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/{mejora_id}/comparison", response_model=PromptComparison)
async def get_prompt_comparison(
    mejora_id: UUID,
    current_user: Dict[str, Any] = Depends(get_current_user_payload)
):
    """Obtener comparación de prompts para una mejora."""
    try:
        service = MejoraAgenteService()
        comparison = await service.get_prompt_comparison(mejora_id, current_user["sub"])
        if not comparison:
            raise HTTPException(status_code=404, detail="Mejora de agente no encontrada")
        return comparison
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/pending/list", response_model=MejoraAgenteListResponse)
async def list_pending_mejoras(
    page: int = Query(1, ge=1, description="Número de página"),
    page_size: int = Query(20, ge=1, le=100, description="Tamaño de página"),
    current_user: Dict[str, Any] = Depends(get_current_user_payload)
):
    """Listar mejoras pendientes de revisión humana."""
    try:
        filters = MejoraAgenteFilters(
            estado=[EstadoMejoraAgente.PENDIENTE_REVISION_HUMANA]
        )

        service = MejoraAgenteService()
        return await service.list_mejoras_agentes(
            current_user["sub"], page, page_size, filters, MejoraAgenteSortBy.FECHA_CREACION_DESC
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
