import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useState, useCallback } from 'react';
import apiClient from '../lib/api';
import {
  MejoraAgenteCreate,
  MejoraAgenteUpdate,
  EstadoMejoraAgente,
  MejoraAgenteSortBy
} from '../types/evaluaciones';

// Query keys
export const mejorasKeys = {
  all: ['mejoras'] as const,
  lists: () => [...mejorasKeys.all, 'list'] as const,
  list: (params: any) => [...mejorasKeys.lists(), params] as const,
  details: () => [...mejorasKeys.all, 'detail'] as const,
  detail: (id: string) => [...mejorasKeys.details(), id] as const,
  comparison: (id: string) => [...mejorasKeys.all, 'comparison', id] as const,
  pending: () => [...mejorasKeys.all, 'pending'] as const,
};

// Hook para listar mejoras de agente
export const useMejoras = (params?: {
  page?: number;
  page_size?: number;
  estado?: EstadoMejoraAgente[];
  agente_id?: string[];
  n8n_workflow_id?: string[];
  fecha_desde?: string;
  fecha_hasta?: string;
  sort_by?: MejoraAgenteSortBy;
}) => {
  // Keep array parameters as arrays for FastAPI compatibility
  const serializedParams = params ? {
    ...params,
    estado: params.estado && params.estado.length > 0 ? params.estado : undefined,
    agente_id: params.agente_id && params.agente_id.length > 0 ? params.agente_id : undefined,
    n8n_workflow_id: params.n8n_workflow_id && params.n8n_workflow_id.length > 0 ? params.n8n_workflow_id : undefined,
  } : undefined;

  return useQuery({
    queryKey: mejorasKeys.list(params),
    queryFn: () => apiClient.mejoras.getAll(serializedParams as any),
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para obtener una mejora específica
export const useMejora = (id: string | null) => {
  return useQuery({
    queryKey: mejorasKeys.detail(id || ''),
    queryFn: () => apiClient.mejoras.getById(id!),
    enabled: !!id && id !== '0' && id.trim() !== '',
  });
};

// Hook para obtener mejoras pendientes
export const useMejorasPendientes = (params?: { page?: number; page_size?: number }) => {
  return useQuery({
    queryKey: mejorasKeys.pending(),
    queryFn: () => apiClient.mejoras.getPending(params),
    staleTime: 2 * 60 * 1000, // 2 minutos (más frecuente para pendientes)
  });
};

// Hook para comparación de prompts
export const usePromptComparison = (id: string) => {
  return useQuery({
    queryKey: mejorasKeys.comparison(id),
    queryFn: () => apiClient.mejoras.getComparison(id),
    enabled: !!id,
  });
};

// Hook para crear mejora
export const useCreateMejora = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: MejoraAgenteCreate) => apiClient.mejoras.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: mejorasKeys.lists() });
      queryClient.invalidateQueries({ queryKey: mejorasKeys.pending() });
    },
  });
};

// Hook para actualizar mejora
export const useUpdateMejora = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: MejoraAgenteUpdate }) =>
      apiClient.mejoras.update(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: mejorasKeys.lists() });
      queryClient.invalidateQueries({ queryKey: mejorasKeys.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: mejorasKeys.pending() });
    },
  });
};

// Hook para eliminar mejora
export const useDeleteMejora = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => apiClient.mejoras.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: mejorasKeys.lists() });
      queryClient.invalidateQueries({ queryKey: mejorasKeys.pending() });
    },
  });
};

// Hook para aplicar mejora
export const useApplyMejora = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => apiClient.mejoras.apply(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: mejorasKeys.lists() });
      queryClient.invalidateQueries({ queryKey: mejorasKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: mejorasKeys.pending() });
      // También invalidar evaluaciones ya que pueden estar relacionadas
      queryClient.invalidateQueries({ queryKey: ['evaluaciones'] });
    },
  });
};

// Hook para descartar mejora
export const useDiscardMejora = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => apiClient.mejoras.discard(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: mejorasKeys.lists() });
      queryClient.invalidateQueries({ queryKey: mejorasKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: mejorasKeys.pending() });
    },
  });
};

// Hook personalizado para manejo de filtros y estado local
export const useMejorasWithFilters = (initialFilters?: any) => {
  const [filters, setFilters] = useState({
    page: initialFilters?.page || 1,
    page_size: initialFilters?.page_size || 20,
    estado: initialFilters?.estado || [] as EstadoMejoraAgente[],
    agente_id: initialFilters?.agente_id || [] as string[],
    n8n_workflow_id: initialFilters?.n8n_workflow_id || [] as string[],
    fecha_desde: initialFilters?.fecha_desde as string | undefined,
    fecha_hasta: initialFilters?.fecha_hasta as string | undefined,
    sort_by: initialFilters?.sort_by || MejoraAgenteSortBy.FECHA_CREACION_DESC,
  });

  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  const mejorasQuery = useMejoras(filters);
  const applyMutation = useApplyMejora();
  const discardMutation = useDiscardMejora();

  const updateFilters = useCallback((newFilters: Partial<typeof filters>) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 })); // Reset page when filters change
  }, []);

  const resetFilters = useCallback(() => {
    setFilters({
      page: 1,
      page_size: 20,
      estado: [],
      agente_id: [],
      n8n_workflow_id: [],
      fecha_desde: undefined,
      fecha_hasta: undefined,
      sort_by: MejoraAgenteSortBy.FECHA_CREACION_DESC,
    });
    setSelectedIds([]);
  }, []);

  const handleApplyMejora = useCallback(async (id: string) => {
    await applyMutation.mutateAsync(id);
  }, [applyMutation]);

  const handleDiscardMejora = useCallback(async (id: string) => {
    await discardMutation.mutateAsync(id);
  }, [discardMutation]);

  const toggleSelection = useCallback((id: string) => {
    setSelectedIds(prev => 
      prev.includes(id) 
        ? prev.filter(selectedId => selectedId !== id)
        : [...prev, id]
    );
  }, []);

  const selectAll = useCallback(() => {
    if (mejorasQuery.data?.mejoras) {
      setSelectedIds(mejorasQuery.data.mejoras.map(m => m.id));
    }
  }, [mejorasQuery.data]);

  const clearSelection = useCallback(() => {
    setSelectedIds([]);
  }, []);

  return {
    // Data
    mejoras: mejorasQuery.data?.mejoras || [],
    total: mejorasQuery.data?.total || 0,
    totalPages: mejorasQuery.data?.total_pages || 0,
    
    // Loading states
    isLoading: mejorasQuery.isLoading,
    isError: mejorasQuery.isError,
    error: mejorasQuery.error,
    isApplying: applyMutation.isPending,
    isDiscarding: discardMutation.isPending,
    
    // Filters
    filters,
    updateFilters,
    resetFilters,
    
    // Selection
    selectedIds,
    toggleSelection,
    selectAll,
    clearSelection,
    
    // Actions
    handleApplyMejora,
    handleDiscardMejora,
    
    // Refetch
    refetch: mejorasQuery.refetch,
  };
};

// Hook para workflow de revisión de mejoras pendientes
export const useMejoraReviewWorkflow = () => {
  const [currentMejoraId, setCurrentMejoraId] = useState<string | null>(null);
  const [reviewMode, setReviewMode] = useState<'list' | 'detail' | 'comparison'>('list');

  const pendientesQuery = useMejorasPendientes();
  const mejoraQuery = useMejora(currentMejoraId || '');
  const comparisonQuery = usePromptComparison(currentMejoraId || '');
  
  const applyMutation = useApplyMejora();
  const discardMutation = useDiscardMejora();

  const startReview = useCallback((mejoraId: string) => {
    setCurrentMejoraId(mejoraId);
    setReviewMode('detail');
  }, []);

  const showComparison = useCallback(() => {
    setReviewMode('comparison');
  }, []);

  const backToList = useCallback(() => {
    setCurrentMejoraId(null);
    setReviewMode('list');
  }, []);

  const handleApprove = useCallback(async () => {
    if (!currentMejoraId) return;
    
    await applyMutation.mutateAsync(currentMejoraId);
    
    // Move to next pending mejora or back to list
    const currentIndex = pendientesQuery.data?.mejoras.findIndex(m => m.id === currentMejoraId) || 0;
    const nextMejora = pendientesQuery.data?.mejoras[currentIndex + 1];
    
    if (nextMejora) {
      setCurrentMejoraId(nextMejora.id);
    } else {
      backToList();
    }
  }, [currentMejoraId, applyMutation, pendientesQuery.data, backToList]);

  const handleDiscard = useCallback(async () => {
    if (!currentMejoraId) return;
    
    await discardMutation.mutateAsync(currentMejoraId);
    
    // Move to next pending mejora or back to list
    const currentIndex = pendientesQuery.data?.mejoras.findIndex(m => m.id === currentMejoraId) || 0;
    const nextMejora = pendientesQuery.data?.mejoras[currentIndex + 1];
    
    if (nextMejora) {
      setCurrentMejoraId(nextMejora.id);
    } else {
      backToList();
    }
  }, [currentMejoraId, discardMutation, pendientesQuery.data, backToList]);

  return {
    // Data
    pendientes: pendientesQuery.data?.mejoras || [],
    currentMejora: mejoraQuery.data,
    comparison: comparisonQuery.data,
    
    // State
    currentMejoraId,
    reviewMode,
    
    // Loading states
    isLoadingPendientes: pendientesQuery.isLoading,
    isLoadingMejora: mejoraQuery.isLoading,
    isLoadingComparison: comparisonQuery.isLoading,
    isApplying: applyMutation.isPending,
    isDiscarding: discardMutation.isPending,
    
    // Actions
    startReview,
    showComparison,
    backToList,
    handleApprove,
    handleDiscard,
    
    // Refetch
    refetchPendientes: pendientesQuery.refetch,
  };
};
