import React, { useState } from 'react';
import { EvaluationsDashboard } from '../components/Evaluations/EvaluationsDashboard';
import { EvaluationsList } from '../components/Evaluations/EvaluationsList';
import { EvaluationsImprovements } from '../components/Evaluations/EvaluationsImprovements';
import { EvaluationDetailModal } from '../components/Evaluations/EvaluationDetailModal';
import { EvaluacionListItem, EstadoEvaluacion } from '../types/evaluaciones';
import { useUpdateEvaluacion } from '../hooks/useEvaluations';

export const EvaluationsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'dashboard' | 'evaluaciones' | 'mejoras'>('dashboard');
  const [selectedEvaluation, setSelectedEvaluation] = useState<EvaluacionListItem | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  // Hook para actualizar evaluaciones
  const updateEvaluacionMutation = useUpdateEvaluacion();

  const tabs = [
    { id: 'dashboard', label: 'Dashboard', icon: '📊' },
    { id: 'evaluaciones', label: 'Evaluaciones', icon: '📋' },
    { id: 'mejoras', label: 'Sugerencias de Mejora', icon: '💡' }
  ];

  const handleSelectEvaluation = (evaluacion: EvaluacionListItem) => {
    setSelectedEvaluation(evaluacion);
    setIsDetailModalOpen(true);
  };

  const handleCloseDetailModal = () => {
    setIsDetailModalOpen(false);
    setSelectedEvaluation(null);
  };

  const handleUpdateEstado = async (id: number, estado: EstadoEvaluacion) => {
    try {
      console.log('Updating evaluation estado:', id, estado);

      // Llamar a la API para actualizar el estado
      const updatedEvaluacion = await updateEvaluacionMutation.mutateAsync({
        id,
        data: { estado }
      });

      // Actualizar el estado local con la respuesta de la API
      if (selectedEvaluation && selectedEvaluation.id === id) {
        setSelectedEvaluation({
          ...selectedEvaluation,
          estado: updatedEvaluacion.estado
        });
      }

      // Cerrar modal después de actualizar exitosamente
      handleCloseDetailModal();
    } catch (error) {
      console.error('Error updating evaluation estado:', error);
      // Aquí podrías mostrar un toast de error al usuario
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <EvaluationsDashboard />;
      case 'evaluaciones':
        return <EvaluationsList onSelectEvaluation={handleSelectEvaluation} />;
      case 'mejoras':
        return <EvaluationsImprovements />;
      default:
        return <EvaluationsDashboard />;
    }
  };

  return (
    <>
      <div className="flex flex-col h-full bg-gray-50">
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <h1 className="text-2xl font-bold text-gray-900">
            Evaluaciones de Agentes IA
          </h1>
          <p className="text-sm text-gray-600 mt-1">
            Gestión y análisis de evaluaciones de rendimiento
          </p>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white border-b border-gray-200 px-6">
          <nav className="flex space-x-8" aria-label="Tabs">
            {tabs.map((tab) => {
              const isActive = activeTab === tab.id;

              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`
                    group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm
                    ${isActive
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                  `}
                  aria-current={isActive ? 'page' : undefined}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="flex-1 p-6">
          {renderTabContent()}
        </div>
      </div>

      {/* Modal de detalle de evaluación */}
      <EvaluationDetailModal
        evaluacion={selectedEvaluation}
        isOpen={isDetailModalOpen}
        onClose={handleCloseDetailModal}
        onUpdateEstado={handleUpdateEstado}
        isUpdating={updateEvaluacionMutation.isPending}
      />
    </>
  );
};
