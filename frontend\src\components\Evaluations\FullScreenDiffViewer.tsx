import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>, Check, ExternalLink, ArrowLeft, ArrowRight, Loader2 } from 'lucide-react';
import { Button } from '../UI/button';
import { Badge } from '../UI/badge';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { MejoraAgenteDetail, EstadoMejoraAgente } from '../../types/evaluaciones';
import * as Diff from 'diff';

interface FullScreenDiffViewerProps {
  mejora: MejoraAgenteDetail;
  isOpen: boolean;
  onClose: () => void;
  onApply?: (id: string) => void;
  onDiscard?: (id: string) => void;
  isApplying?: boolean;
  isDiscarding?: boolean;
}



interface DiffViewProps {
  originalContent: string;
  improvedContent: string;
  onCopyOriginal: () => void;
  onCopyImproved: () => void;
  copiedOriginal: boolean;
  copiedImproved: boolean;
}

interface DiffPart {
  type: 'added' | 'removed' | 'unchanged';
  value: string;
}

const DiffView: React.FC<DiffViewProps> = ({
  originalContent,
  improvedContent,
  onCopyOriginal,
  onCopyImproved,
  copiedOriginal,
  copiedImproved
}) => {
  const generateWordDiff = (original: string, improved: string): DiffPart[] => {
    if (!original && !improved) {
      return [{ type: 'unchanged', value: 'Sin contenido disponible' }];
    }

    if (!original) {
      return [{ type: 'added', value: improved }];
    }

    if (!improved) {
      return [{ type: 'removed', value: original }];
    }

    const diff = Diff.diffWords(original, improved);
    return diff.map(part => ({
      type: part.added ? 'added' : part.removed ? 'removed' : 'unchanged',
      value: part.value
    }));
  };

  const wordDiff = generateWordDiff(originalContent, improvedContent);

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Unified Diff View - Top Section - Large Height */}
      <div className="flex-[2] flex flex-col border-b border-gray-200 min-h-0 bg-white rounded-t-lg shadow-sm">
        <div className="flex-shrink-0 flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg">
          <div className="flex items-center space-x-4">
            <h3 className="font-semibold text-base text-blue-900">
              Vista Diff Unificada
            </h3>
            <div className="flex items-center space-x-4 text-sm text-blue-700">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-red-100 border border-red-300 rounded-full"></div>
                <span className="font-medium">Eliminado</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-100 border border-green-300 rounded-full"></div>
                <span className="font-medium">Añadido</span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto bg-white p-6 min-h-0">
          <div className="font-mono text-sm leading-7 max-w-none">
            {wordDiff.map((part, index) => (
              <span
                key={index}
                className={
                  part.type === 'added'
                    ? 'bg-green-100 text-green-800 px-1.5 py-0.5 rounded-md font-medium border border-green-200'
                    : part.type === 'removed'
                    ? 'bg-red-100 text-red-800 px-1.5 py-0.5 rounded-md line-through font-medium border border-red-200'
                    : 'text-gray-700'
                }
                style={{ whiteSpace: 'pre-wrap' }}
              >
                {part.value}
              </span>
            ))}
          </div>
        </div>
      </div>

      {/* Side by Side Comparison - Bottom Section */}
      <div className="flex-[2] border-t border-gray-200 flex flex-col min-h-0 bg-white rounded-b-lg shadow-sm mt-4" style={{ minHeight: '700px' }}>
        <div className="flex-shrink-0 bg-gradient-to-r from-gray-50 to-slate-50 px-6 py-3 border-b border-gray-200 rounded-t-lg">
          <h4 className="text-base font-semibold text-gray-800">Comparación Lado a Lado</h4>
        </div>
        <div className="flex flex-1 min-h-0">
          {/* Original */}
          <div className="flex-1 border-r border-gray-200 flex flex-col min-h-0">
            <div className="flex-shrink-0 bg-gradient-to-r from-red-50 to-rose-50 px-6 py-3 border-b border-red-100">
              <div className="flex items-center justify-between">
                <span className="text-sm font-semibold text-red-800">Prompt Original</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onCopyOriginal}
                  className="h-8 px-3 text-xs hover:bg-red-100 text-red-700 rounded-lg border border-red-200 hover:border-red-300 transition-all duration-200"
                >
                  {copiedOriginal ? <Check className="h-3 w-3 mr-1.5" /> : <Copy className="h-3 w-3 mr-1.5" />}
                  {copiedOriginal ? 'Copiado' : 'Copiar'}
                </Button>
              </div>
            </div>
            <div className="flex-1 overflow-y-auto bg-white p-6 min-h-0">
              <pre className="text-sm whitespace-pre-wrap break-words text-gray-700 font-mono leading-6">
                {originalContent || 'Sin contenido disponible'}
              </pre>
            </div>
          </div>

          {/* Improved */}
          <div className="flex-1 flex flex-col min-h-0">
            <div className="flex-shrink-0 bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-3 border-b border-green-100">
              <div className="flex items-center justify-between">
                <span className="text-sm font-semibold text-green-800">Prompt Mejorado</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onCopyImproved}
                  className="h-8 px-3 text-xs hover:bg-green-100 text-green-700 rounded-lg border border-green-200 hover:border-green-300 transition-all duration-200"
                >
                  {copiedImproved ? <Check className="h-3 w-3 mr-1.5" /> : <Copy className="h-3 w-3 mr-1.5" />}
                  {copiedImproved ? 'Copiado' : 'Copiar'}
                </Button>
              </div>
            </div>
            <div className="flex-1 overflow-y-auto bg-white p-6 min-h-0">
              <pre className="text-sm whitespace-pre-wrap break-words text-gray-700 font-mono leading-6">
                {improvedContent || 'Sin contenido disponible'}
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};



interface TabButtonProps {
  active: boolean;
  onClick: () => void;
  children: React.ReactNode;
}

const TabButton: React.FC<TabButtonProps> = ({ active, onClick, children }) => {
  return (
    <button
      onClick={onClick}
      className={`px-4 py-2 text-sm font-medium rounded-t-lg border-b-2 transition-colors ${
        active
          ? 'bg-blue-50 text-blue-700 border-blue-500'
          : 'text-gray-500 border-transparent hover:text-gray-700 hover:bg-gray-50'
      }`}
    >
      {children}
    </button>
  );
};

const getEstadoBadge = (estado: EstadoMejoraAgente) => {
  const variants = {
    [EstadoMejoraAgente.PENDIENTE_REVISION_HUMANA]: { variant: 'secondary' as const, label: 'Pendiente Revisión' },
    [EstadoMejoraAgente.APROBADO_PARA_APLICAR]: { variant: 'default' as const, label: 'Aprobado para Aplicar' },
    [EstadoMejoraAgente.DESCARTADO]: { variant: 'destructive' as const, label: 'Descartado' },
    [EstadoMejoraAgente.APLICADO]: { variant: 'outline' as const, label: 'Aplicado' },
  };

  const config = variants[estado] || { variant: 'secondary' as const, label: estado };
  return <Badge variant={config.variant}>{config.label}</Badge>;
};

export const FullScreenDiffViewer: React.FC<FullScreenDiffViewerProps> = ({
  mejora,
  isOpen,
  onClose,
  onApply,
  onDiscard,
  isApplying = false,
  isDiscarding = false,
}) => {
  const [copiedText, setCopiedText] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'system' | 'user'>('system');

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(type);
      setTimeout(() => setCopiedText(null), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-white flex flex-col">
      {/* Modern Header - Fixed */}
      <div className="flex-shrink-0 flex items-center justify-between p-6 border-b bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <ArrowLeft className="h-5 w-5 text-blue-600" />
              <ArrowRight className="h-5 w-5 text-blue-600 -ml-2" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">Comparación de Prompts</h1>
              <p className="text-sm text-gray-600">
                {mejora.n8n_workflow_id || 'Sin workflow'} • {mejora.nombre_agente_amigable || 'Sin agente'}
              </p>
            </div>
          </div>
          {getEstadoBadge(mejora.estado)}
        </div>
        <Button variant="ghost" size="sm" onClick={onClose} className="h-10 w-10 p-0">
          <X className="h-5 w-5" />
        </Button>
      </div>

      {/* Scrollable Content Area */}
      <div className="flex-1 overflow-y-auto min-h-0">
        {/* Metadata Card */}
        <div className="p-6 bg-white border-b">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">Workflow ID</div>
              <div className="text-sm font-mono text-gray-900 mt-1">{mejora.n8n_workflow_id || 'N/A'}</div>
            </div>
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">Agente ID</div>
              <div className="text-sm font-mono text-gray-900 mt-1">{mejora.agente_id || 'N/A'}</div>
            </div>
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">Estado</div>
              <div className="text-sm text-gray-900 mt-1">{mejora.estado}</div>
            </div>
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">Fecha</div>
              <div className="text-sm text-gray-900 mt-1">
                {format(new Date(mejora.created_at), 'dd/MM/yyyy HH:mm', { locale: es })}
              </div>
            </div>
          </div>

          {mejora.explicacion_mejoras && (
            <div className="bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-lg p-4">
              <h3 className="font-semibold text-amber-900 mb-2 flex items-center gap-2">
                <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                Explicación de mejoras
              </h3>
              <p className="text-amber-800 text-sm leading-relaxed">{mejora.explicacion_mejoras}</p>
            </div>
          )}
        </div>

        {/* Tab Navigation */}
        <div className="border-b bg-white sticky top-0 z-10">
          <div className="flex px-6">
            <TabButton
              active={activeTab === 'system'}
              onClick={() => setActiveTab('system')}
            >
              System Prompt
            </TabButton>
            <TabButton
              active={activeTab === 'user'}
              onClick={() => setActiveTab('user')}
            >
              User Prompt
            </TabButton>
          </div>
        </div>

        {/* Diff Content Area - Takes remaining space */}
        <div className="bg-gray-50 p-6" style={{ height: 'calc(100vh - 200px)', minHeight: '1600px' }}>
          {activeTab === 'system' && (
            <DiffView
              originalContent={mejora.system_prompt_original || ''}
              improvedContent={mejora.system_prompt_mejorado || ''}
              onCopyOriginal={() => copyToClipboard(mejora.system_prompt_original || '', 'system-original')}
              onCopyImproved={() => copyToClipboard(mejora.system_prompt_mejorado || '', 'system-improved')}
              copiedOriginal={copiedText === 'system-original'}
              copiedImproved={copiedText === 'system-improved'}
            />
          )}

          {activeTab === 'user' && (
            <DiffView
              originalContent={mejora.user_prompt_original || ''}
              improvedContent={mejora.user_prompt_mejorado || ''}
              onCopyOriginal={() => copyToClipboard(mejora.user_prompt_original || '', 'user-original')}
              onCopyImproved={() => copyToClipboard(mejora.user_prompt_mejorado || '', 'user-improved')}
              copiedOriginal={copiedText === 'user-original'}
              copiedImproved={copiedText === 'user-improved'}
            />
          )}
        </div>
      </div>

      {/* Modern Footer - Fixed */}
      <div className="flex-shrink-0 border-t bg-white p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {mejora.n8n_workflow_id && (
              <Button variant="outline" size="sm" className="gap-2">
                <ExternalLink className="h-4 w-4" />
                Ver en n8n
              </Button>
            )}
            <div className="text-sm text-gray-500">
              ID: <span className="font-mono">{mejora.id.substring(0, 8)}...</span>
            </div>
          </div>

          <div className="flex items-center gap-3">
            {mejora.estado === EstadoMejoraAgente.PENDIENTE_REVISION_HUMANA && (
              <>
                <Button
                  variant="outline"
                  onClick={() => onDiscard?.(mejora.id)}
                  disabled={isDiscarding || isApplying}
                  className="border-red-200 text-red-700 hover:bg-red-50 disabled:opacity-50"
                >
                  {isDiscarding ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Descartando...
                    </>
                  ) : (
                    'Descartar'
                  )}
                </Button>
                <Button
                  onClick={() => onApply?.(mejora.id)}
                  disabled={isApplying || isDiscarding}
                  className="bg-green-600 hover:bg-green-700 text-white disabled:opacity-50"
                >
                  {isApplying ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Aplicando...
                    </>
                  ) : (
                    'Aplicar mejora'
                  )}
                </Button>
              </>
            )}
            <Button variant="outline" onClick={onClose}>
              Cerrar
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
