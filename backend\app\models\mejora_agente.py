from pydantic import BaseModel, Field
from typing import Optional, List
from uuid import UUID
from datetime import datetime
from enum import Enum

class EstadoMejoraAgente(str, Enum):
    """Estados posibles para una mejora de agente."""
    PENDIENTE_REVISION_HUMANA = "pendiente_revision_humana"
    APROBADO_PARA_APLICAR = "aprobado_para_aplicar"
    DESCARTADO = "descartado"
    APLICADO = "aplicado"

class MejoraAgenteBase(BaseModel):
    """Modelo base para mejoras de agente."""
    agente_id: Optional[UUID] = None
    n8n_workflow_id: str
    system_prompt_mejorado: Optional[str] = None
    user_prompt_mejorado: Optional[str] = None
    explicacion_mejoras: str
    estado: EstadoMejoraAgente = EstadoMejoraAgente.PENDIENTE_REVISION_HUMANA

class MejoraAgenteCreate(MejoraAgenteBase):
    """Modelo para crear una nueva mejora de agente."""
    pass

class MejoraAgenteUpdate(BaseModel):
    """Modelo para actualizar una mejora de agente."""
    agente_id: Optional[UUID] = None
    n8n_workflow_id: Optional[str] = None
    system_prompt_mejorado: Optional[str] = None
    user_prompt_mejorado: Optional[str] = None
    explicacion_mejoras: Optional[str] = None
    estado: Optional[EstadoMejoraAgente] = None

class MejoraAgenteInDB(MejoraAgenteBase):
    """Modelo para mejoras de agente en la base de datos."""
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class MejoraAgente(MejoraAgenteInDB):
    """Modelo completo de mejora de agente para respuestas API."""
    pass

class MejoraAgenteListItem(BaseModel):
    """Modelo simplificado para listas de mejoras de agente."""
    id: UUID
    agente_id: Optional[UUID] = None
    n8n_workflow_id: str
    explicacion_mejoras: str
    estado: EstadoMejoraAgente
    created_at: datetime
    updated_at: Optional[datetime] = None
    # Campos adicionales para la UI
    nombre_agente_amigable: Optional[str] = None
    evaluaciones_relacionadas: Optional[int] = None  # Número de evaluaciones que generaron esta mejora

class MejoraAgenteDetail(MejoraAgente):
    """Modelo detallado de mejora de agente con información adicional."""
    # Prompts originales para comparación
    system_prompt_original: Optional[str] = None
    user_prompt_original: Optional[str] = None
    nombre_agente_amigable: Optional[str] = None
    evaluaciones_relacionadas: List[int] = []  # IDs de evaluaciones relacionadas

class MejoraAgenteFilters(BaseModel):
    """Filtros para la lista de mejoras de agente."""
    estado: Optional[List[EstadoMejoraAgente]] = None
    agente_id: Optional[List[UUID]] = None
    n8n_workflow_id: Optional[List[str]] = None
    fecha_desde: Optional[datetime] = None
    fecha_hasta: Optional[datetime] = None

class MejoraAgenteSortBy(str, Enum):
    """Opciones de ordenamiento para mejoras de agente."""
    FECHA_CREACION_ASC = "fecha_creacion_asc"
    FECHA_CREACION_DESC = "fecha_creacion_desc"
    FECHA_ACTUALIZACION_ASC = "fecha_actualizacion_asc"
    FECHA_ACTUALIZACION_DESC = "fecha_actualizacion_desc"
    AGENTE_ASC = "agente_asc"
    AGENTE_DESC = "agente_desc"
    ESTADO_ASC = "estado_asc"
    ESTADO_DESC = "estado_desc"

class MejoraAgenteListResponse(BaseModel):
    """Respuesta para la lista de mejoras de agente."""
    mejoras: List[MejoraAgenteListItem]
    total: int
    page: int
    page_size: int
    total_pages: int

class MejoraAgenteApplyRequest(BaseModel):
    """Modelo para solicitar la aplicación de una mejora."""
    mejora_id: UUID
    confirmar_aplicacion: bool = True

class MejoraAgenteApplyResponse(BaseModel):
    """Respuesta para la aplicación de una mejora."""
    success: bool
    message: str
    webhook_response: Optional[dict] = None
    mejora_actualizada: Optional[MejoraAgente] = None

class PromptComparison(BaseModel):
    """Modelo para comparar prompts originales vs mejorados."""
    system_prompt_original: Optional[str] = None
    system_prompt_propuesto: Optional[str] = None  # Mantenemos este nombre para compatibilidad con frontend
    user_prompt_original: Optional[str] = None
    user_prompt_propuesto: Optional[str] = None    # Mantenemos este nombre para compatibilidad con frontend
    explicacion_mejoras: str
    agente_id: Optional[UUID] = None
    nombre_agente_amigable: Optional[str] = None

class MejoraAgenteStats(BaseModel):
    """Estadísticas de mejoras de agente."""
    total_mejoras: int
    mejoras_por_estado: dict[EstadoMejoraAgente, int]
    mejoras_por_agente: dict[str, int]
    mejoras_aplicadas_exitosas: int
    mejoras_descartadas: int
