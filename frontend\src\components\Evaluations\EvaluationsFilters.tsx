import React from 'react';
import { UnifiedFilters } from './UnifiedFilters';

interface EvaluationsFiltersProps {
  filters: any; // TODO: Usar tipo correcto cuando esté disponible
  onFilterChange: (key: string, value: any) => void;
  onClearFilters: () => void;
}

export const EvaluationsFilters: React.FC<EvaluationsFiltersProps> = ({
  filters,
  onFilterChange,
  onClearFilters
}) => {
  return (
    <UnifiedFilters
      filters={filters}
      onFilterChange={onFilterChange}
      onClearFilters={onClearFilters}
      showEstado={true}
      showWorkflows={true}
      showEtiquetas={true}
      showSugerencias={true}
      showPuntuacion={true}
      showFechas={true}
      showAgente={false}
      estadoType="evaluaciones"
      title="Filtros"
    />
  );
};


