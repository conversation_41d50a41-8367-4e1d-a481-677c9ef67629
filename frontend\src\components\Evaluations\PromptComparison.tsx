import React, { useState } from 'react';
import ReactDiffViewer, { DiffMethod } from 'react-diff-viewer';
import { 
  FileText, 
  User, 
  Settings, 
  Eye, 
  EyeOff,
  ArrowLeftRight,
  Copy,
  Check
} from 'lucide-react';
import { But<PERSON> } from '../UI/button';
import { Card, CardContent, CardHeader, CardTitle } from '../UI/card';
import { ScrollArea } from '../UI/scroll-area';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../UI/tabs';
import { Badge } from '../UI/badge';
import { Separator } from '../UI/separator';

import type { MejoraAgente } from '../../types/evaluaciones';

interface PromptComparisonProps {
  mejora: MejoraAgente;
  showLineNumbers?: boolean;
  splitView?: boolean;
}

export const PromptComparison: React.FC<PromptComparisonProps> = ({
  mejora,
  showLineNumbers = true,
  splitView = true,
}) => {
  const [viewMode, setViewMode] = useState<'split' | 'unified'>(splitView ? 'split' : 'unified');
  const [showWhitespace, setShowWhitespace] = useState(false);
  const [copiedField, setCopiedField] = useState<string | null>(null);

  // Verificar si hay prompts para comparar
  const hasUserPrompts = mejora.user_prompt_original || mejora.user_prompt_mejorado;
  const hasSystemPrompts = mejora.system_prompt_original || mejora.system_prompt_mejorado;

  const copyToClipboard = async (text: string, field: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(field);
      setTimeout(() => setCopiedField(null), 2000);
    } catch (error) {
      console.error('Error copying to clipboard:', error);
    }
  };

  const renderPromptCard = (
    title: string,
    originalPrompt: string | null,
    improvedPrompt: string | null,
    type: 'user' | 'system'
  ) => {
    if (!originalPrompt && !improvedPrompt) {
      return (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-sm">
              {type === 'user' ? <User className="h-4 w-4" /> : <Settings className="h-4 w-4" />}
              {title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8 text-gray-500">
              <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No hay prompts {type === 'user' ? 'de usuario' : 'de sistema'} para comparar</p>
            </div>
          </CardContent>
        </Card>
      );
    }

    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-sm">
              {type === 'user' ? <User className="h-4 w-4" /> : <Settings className="h-4 w-4" />}
              {title}
            </CardTitle>
            <div className="flex items-center gap-2">
              {improvedPrompt && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => copyToClipboard(improvedPrompt, `${type}-improved`)}
                  className="h-8"
                >
                  {copiedField === `${type}-improved` ? (
                    <Check className="h-3 w-3" />
                  ) : (
                    <Copy className="h-3 w-3" />
                  )}
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {originalPrompt && improvedPrompt ? (
            // Mostrar comparación cuando hay ambos prompts
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge variant="outline">Comparación</Badge>
                  <span className="text-xs text-gray-500">
                    Original vs Mejorado
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setViewMode(viewMode === 'split' ? 'unified' : 'split')}
                    className="h-8"
                  >
                    <ArrowLeftRight className="h-3 w-3 mr-1" />
                    {viewMode === 'split' ? 'Unificado' : 'Dividido'}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowWhitespace(!showWhitespace)}
                    className="h-8"
                  >
                    {showWhitespace ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                  </Button>
                </div>
              </div>
              
              <div className="border rounded-lg overflow-hidden">
                <ReactDiffViewer
                  oldValue={originalPrompt}
                  newValue={improvedPrompt}
                  splitView={viewMode === 'split'}
                  compareMethod={DiffMethod.WORDS}
                  showDiffOnly={false}
                  hideLineNumbers={!showLineNumbers}
                  leftTitle="Prompt Original"
                  rightTitle="Prompt Mejorado"
                  styles={{
                    variables: {
                      light: {
                        codeFoldGutterBackground: '#f8f9fa',
                        codeFoldBackground: '#f8f9fa',
                        addedBackground: '#e6ffed',
                        addedColor: '#24292e',
                        removedBackground: '#ffeef0',
                        removedColor: '#24292e',
                        wordAddedBackground: '#acf2bd',
                        wordRemovedBackground: '#fdb8c0',
                        addedGutterBackground: '#cdffd8',
                        removedGutterBackground: '#fdbdbe',
                        gutterBackground: '#f8f9fa',
                        gutterBackgroundDark: '#f8f9fa',
                        highlightBackground: '#fffbdd',
                        highlightGutterBackground: '#fff5b4',
                      },
                    },
                    contentText: {
                      fontSize: '13px',
                      lineHeight: '1.4',
                      fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                    },
                  }}
                />
              </div>
            </div>
          ) : (
            // Mostrar solo el prompt disponible
            <div className="space-y-4">
              {originalPrompt && (
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Badge variant="secondary">Prompt Original</Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(originalPrompt, `${type}-original`)}
                      className="h-8"
                    >
                      {copiedField === `${type}-original` ? (
                        <Check className="h-3 w-3" />
                      ) : (
                        <Copy className="h-3 w-3" />
                      )}
                    </Button>
                  </div>
                  <ScrollArea className="h-48 border rounded-lg p-4 bg-gray-50">
                    <pre className="text-sm whitespace-pre-wrap font-mono">
                      {originalPrompt}
                    </pre>
                  </ScrollArea>
                </div>
              )}
              
              {improvedPrompt && (
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Badge variant="default">Prompt Mejorado</Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(improvedPrompt, `${type}-improved`)}
                      className="h-8"
                    >
                      {copiedField === `${type}-improved` ? (
                        <Check className="h-3 w-3" />
                      ) : (
                        <Copy className="h-3 w-3" />
                      )}
                    </Button>
                  </div>
                  <ScrollArea className="h-48 border rounded-lg p-4 bg-green-50">
                    <pre className="text-sm whitespace-pre-wrap font-mono">
                      {improvedPrompt}
                    </pre>
                  </ScrollArea>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  if (!hasUserPrompts && !hasSystemPrompts) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No hay prompts para comparar
          </h3>
          <p className="text-gray-500">
            Esta mejora no incluye cambios en los prompts del agente.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold">Comparación de Prompts</h2>
          <p className="text-gray-500 text-sm">
            Agente: {mejora.nombre_agente_amigable}
          </p>
        </div>
        <Badge variant="outline" className="flex items-center gap-1">
          <FileText className="h-3 w-3" />
          {mejora.tipo_mejora}
        </Badge>
      </div>

      <Separator />

      {/* Explicación de Mejoras */}
      {mejora.explicacion_mejoras && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Explicación de Mejoras</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-sm text-blue-900 whitespace-pre-wrap">
                {mejora.explicacion_mejoras}
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Tabs for different prompt types */}
      <Tabs defaultValue="user" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="user" disabled={!hasUserPrompts}>
            <User className="h-4 w-4 mr-2" />
            Prompts de Usuario
          </TabsTrigger>
          <TabsTrigger value="system" disabled={!hasSystemPrompts}>
            <Settings className="h-4 w-4 mr-2" />
            Prompts de Sistema
          </TabsTrigger>
        </TabsList>

        <TabsContent value="user" className="space-y-4">
          {renderPromptCard(
            'Prompts de Usuario',
            mejora.user_prompt_original || null,
            mejora.user_prompt_mejorado || null,
            'user'
          )}
        </TabsContent>

        <TabsContent value="system" className="space-y-4">
          {renderPromptCard(
            'Prompts de Sistema',
            mejora.system_prompt_original || null,
            mejora.system_prompt_mejorado || null,
            'system'
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};
