import React, { useState, useEffect, ReactNode, useCallback, useMemo, useRef } from 'react';
import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { supabase } from '../services/supabaseClient';
import { ChatContext, ChatContextProps, ChatMessage } from '../contexts/ChatContext';
import { useAuth } from '../hooks/useAuth';
import { useThreadHistory } from '../hooks/useThreadHistory'; // Import the new hook
import { ThreadMetadata } from '../types/chat';
import { API_BASE_URL } from '../config/api';

// Define the provider component props
interface ChatProviderProps {
  children: ReactNode;
}
// Interface representing the raw data structure from Supabase 'threads' table
interface RawChatMessage {
  thread_id: number | string | bigint; // Handle potential int8 type
  content: string | null;
  type: string | null;
  from: 'User' | 'Agent' | null; // DB column name
  message_id: number | null;
  agent_id: string | null;
  user_id: string | null;
  created_at: string;
  // Add the primary key column you added (e.g., 'id') if it's included in the payload
  id?: string; // PK from 'threads' table is UUID, so it's a string
}
 
// Interface for the payload from 'doc_exports' Realtime events
interface DocExportRealtimePayload {
  id: string; // PK of doc_exports
  threads_row_id: string; // FK to threads.id (this is what we map to message.id)
  user_id: string;
  status: 'pending' | 'success' | 'error';
  doc_url?: string | null;
  created_at: string;
  updated_at: string;
}


 
// Create the provider component
export const ChatProvider: React.FC<ChatProviderProps> = ({ children }) => {
  // Auth hook - must be at the top
  const { user, session } = useAuth();

  const [currentThreadId, setCurrentThreadId] = useState<number | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [selectedAgentId, setSelectedAgentId] = useState<string | null>(null);
  const [isProcessingMessage, setIsProcessingMessage] = useState<boolean>(false);
  const [isContextPanelOpen, setIsContextPanelOpen] = useState<boolean>(false);
  const [isAgentSelectionRequired, setIsAgentSelectionRequired] = useState<boolean>(false);
  // Thread title state
  const [currentThreadTitle, setCurrentThreadTitle] = useState<string | null>(null);
  const [isLoadingTitle, setIsLoadingTitle] = useState<boolean>(false);

  // --- TanStack Query for History ---
  const { data: historyData, isLoading: isLoadingHistory } = useThreadHistory(currentThreadId);

  // Performance optimization constants
  const MAX_MESSAGES_IN_MEMORY = 100; // Limit messages in active thread

  // State for "Send to Docs" feature is handled directly on ChatMessage objects.
  // The separate docExportStatuses state was unused.

  // --- Context Functions ---
 
  // Custom setter for agent ID to manage the required flag - memoized
  const handleSetSelectedAgentId = useCallback((agentId: string | null) => {
    setSelectedAgentId(agentId);
    if (agentId) {
      setIsAgentSelectionRequired(false); // Agent selected, requirement met
    }
  }, []);

  const addMessage = useCallback((message: ChatMessage) => {
    setMessages((prevMessages) => {
      const newMessages = [...prevMessages, message];

      // Automatic cleanup if too many messages in memory
      if (newMessages.length > MAX_MESSAGES_IN_MEMORY * 1.5) {
        console.warn(`Too many messages in memory (${newMessages.length}), cleaning up...`);
        return newMessages.slice(-MAX_MESSAGES_IN_MEMORY);
      }

      return newMessages;
    });
  }, [MAX_MESSAGES_IN_MEMORY]);

  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  const startProcessing = useCallback(() => {
    setIsProcessingMessage(true);
  }, []);

  const stopProcessing = useCallback(() => {
    setIsProcessingMessage(false);
  }, []);

  const toggleContextPanel = useCallback(() => {
    setIsContextPanelOpen((prev) => !prev);
  }, []);

  const startNewConversation = useCallback(() => {
    console.log("Starting new conversation...");
    setCurrentThreadId(null); // Reset thread ID
    setMessages([]); // Clear messages
    setIsProcessingMessage(false);
    setSelectedAgentId(null); // Reset selected agent
    setIsAgentSelectionRequired(true); // Require agent selection for new chat
    setCurrentThreadTitle(null); // Reset title
  }, []);

  // Function to fetch thread metadata
  const fetchThreadMetadata = useCallback(async (threadId: number) => {
    if (!session?.access_token) return;

    try {
      setIsLoadingTitle(true);
      const response = await fetch(`${API_BASE_URL}/threads/${threadId}/metadata`, {
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      });

      if (response.ok) {
        const metadata: ThreadMetadata = await response.json();
        setCurrentThreadTitle(metadata.titulo);
      } else if (response.status === 404) {
        // Metadata doesn't exist yet, set default title
        setCurrentThreadTitle('Chat sin título');
      } else {
        console.error('Failed to fetch thread metadata:', response.statusText);
        setCurrentThreadTitle('Chat sin título');
      }
    } catch (error) {
      console.error('Error fetching thread metadata:', error);
      setCurrentThreadTitle('Chat sin título');
    } finally {
      setIsLoadingTitle(false);
    }
  }, [session?.access_token]);

  // Function to update thread title
  const updateThreadTitle = useCallback(async (newTitle: string) => {
    if (!currentThreadId || !session?.access_token) {
      throw new Error('No active thread or user not authenticated');
    }

    try {
      setIsLoadingTitle(true);
      const response = await fetch(`${API_BASE_URL}/threads/${currentThreadId}/metadata`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({ titulo: newTitle })
      });

      if (response.ok) {
        const updatedMetadata: ThreadMetadata = await response.json();
        setCurrentThreadTitle(updatedMetadata.titulo);
      } else {
        throw new Error(`Failed to update title: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error updating thread title:', error);
      throw error;
    } finally {
      setIsLoadingTitle(false);
    }
  }, [currentThreadId, session?.access_token]);

  // Effect to fetch thread metadata when thread changes
  useEffect(() => {
    if (currentThreadId) {
      fetchThreadMetadata(currentThreadId);
    } else {
      setCurrentThreadTitle(null);
    }
  }, [currentThreadId, fetchThreadMetadata]);

  // --- Helper Function to Group Messages ---
  const groupMessages = useCallback((allMessages: ChatMessage[]): ChatMessage[] => {
    const messageMap: { [key: number]: ChatMessage } = {};
    const intermediateSteps: ChatMessage[] = [];
    const finalGroupedMessages: ChatMessage[] = [];

    // First pass: Identify answers and separate intermediate steps
    allMessages.forEach(msg => {
      // Ensure message_id is treated as number for map keys if it exists
      const messageIdNum = typeof msg.message_id === 'string' ? parseInt(msg.message_id, 10) : msg.message_id;

      if (msg.from_sender === 'User' || msg.type === 'answer') {
        // Keep user messages and final answers, initialize steps array for answers
        const messageToAdd = {
          ...msg,
          message_id: messageIdNum, // Ensure message_id is number
          intermediate_steps: msg.type === 'answer' ? [] : undefined,
        };
        if (msg.type === 'answer' && messageIdNum) {
          messageMap[messageIdNum] = messageToAdd; // Map answers by message_id
        }
        finalGroupedMessages.push(messageToAdd); // Add to final list directly
      } else if (msg.from_sender === 'Agent' && messageIdNum) {
        // Collect intermediate steps
        intermediateSteps.push({...msg, message_id: messageIdNum}); // Ensure message_id is number
      } else {
         // Keep other messages (e.g., older user messages without type)
         finalGroupedMessages.push({...msg, message_id: messageIdNum});
      }
    });

    // Second pass: Assign intermediate steps to their parent answer
    intermediateSteps.forEach(step => {
      if (step.message_id && messageMap[step.message_id]) {
        // Ensure the steps array exists
        if (!messageMap[step.message_id].intermediate_steps) {
            messageMap[step.message_id].intermediate_steps = [];
        }
        messageMap[step.message_id].intermediate_steps!.push(step);
        // Sort steps by creation time? Optional, depends on desired order.
        messageMap[step.message_id].intermediate_steps!.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
      } else {
          console.warn("Intermediate step found without matching answer message_id:", step);
      }
    });

     // Update the final list with the answers that now contain steps
     const updatedFinalMessages = finalGroupedMessages.map(msg => {
         if (msg.type === 'answer' && msg.message_id && messageMap[msg.message_id]) {
             return messageMap[msg.message_id]; // Return the answer message with nested steps
         }
         return msg; // Return other messages (user, orphaned steps if added)
     });

     // Sort the final list by creation date
     updatedFinalMessages.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());


    console.log("Grouped messages:", updatedFinalMessages);
    return updatedFinalMessages;
  }, []);

  // --- Effect to update messages from TanStack Query ---
  useEffect(() => {
    if (historyData) {
      const grouped = groupMessages(historyData);
      setMessages(grouped);
    } else if (!isLoadingHistory) {
      setMessages([]);
    }
  }, [historyData, isLoadingHistory, groupMessages]);


  // Handle postgres_changes messages (works automatically with database triggers)
  const handleRealtimeMessage = useCallback((payload: RealtimePostgresChangesPayload<RawChatMessage>) => {
    // Extract the message data from postgres_changes payload
    const messageData = payload.new as RawChatMessage;

    if (!messageData || Object.keys(messageData).length === 0) {
      console.warn('No message data in postgres_changes payload');
      return;
    }

    // Check if this message belongs to the current thread
    const incomingThreadId = messageData.thread_id;
    const incomingIdStr = incomingThreadId?.toString();
    const currentIdStr = currentThreadId?.toString();

    if (!incomingIdStr || incomingIdStr !== currentIdStr) {
      console.log(`Received message for different thread. Incoming: ${incomingIdStr}, Current: ${currentIdStr}`);
      return;
    }

    // Map to ChatMessage structure
    const mappedMessage: ChatMessage = {
      id: messageData.id?.toString(),
      thread_id: incomingThreadId,
      content: messageData.content,
      type: messageData.type,
      from_sender: messageData.from,
      message_id: messageData.message_id,
      agent_id: messageData.agent_id,
      user_id: messageData.user_id,
      created_at: messageData.created_at,
    };

    // Handle Agent messages (Answers and Intermediate Steps)
    if (mappedMessage.from_sender === 'Agent') {
      // Ensure message_id is a number for comparisons and lookups
      const messageIdNum = typeof mappedMessage.message_id === 'string'
        ? parseInt(mappedMessage.message_id, 10)
        : mappedMessage.message_id;

      const messageWithNumericId = { ...mappedMessage, message_id: messageIdNum };

      if (messageWithNumericId.type === 'answer') {
        console.log(`📨 Received final answer (ID: ${messageWithNumericId.id})`);
        // Add the answer message (initially without steps)
        setMessages(prevMessages => [...prevMessages, { ...messageWithNumericId, intermediate_steps: [] }]);
        stopProcessing();
      } else if (messageWithNumericId.message_id) {
        // This is an intermediate step, add it to the parent answer
        console.log(`🔧 Received ${messageWithNumericId.type} step (ID: ${messageWithNumericId.id})`);
        setMessages(prevMessages => {
          const parentIndex = prevMessages.findIndex(
            msg => msg.type === 'answer' && msg.message_id === messageWithNumericId.message_id
          );
          if (parentIndex > -1) {
            const newMessages = [...prevMessages];
            const parentMessage = { ...newMessages[parentIndex] };
            // Ensure intermediate_steps array exists
            parentMessage.intermediate_steps = parentMessage.intermediate_steps || [];
            parentMessage.intermediate_steps.push(messageWithNumericId);
            // Sort steps within the parent
            parentMessage.intermediate_steps.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
            newMessages[parentIndex] = parentMessage;
            console.log(`✅ Added ${messageWithNumericId.type} step to answer (${parentMessage.intermediate_steps.length} total steps)`);
            return newMessages;
          } else {
            console.warn("⚠️ Intermediate step received but parent answer not found yet.", messageWithNumericId);
            return prevMessages; // Don't add if parent not found
          }
        });
      } else {
        console.warn("⚠️ Received agent message with no type or message_id", messageWithNumericId);
      }
    } else {
      // Ignore user messages from Realtime
      console.log("👤 Ignoring user message (already added optimistically)");
    }
  }, [currentThreadId, stopProcessing]);

  // --- Supabase Realtime Subscription using postgres_changes ---
  useEffect(() => {
    let channel: RealtimeChannel | null = null;

    if (currentThreadId) {
      console.log(`Subscribing to postgres_changes for thread_id: ${currentThreadId}`);

      const channelName = `thread_changes:${currentThreadId}`;
      channel = supabase.channel(channelName);

      // Set up postgres_changes listener for new messages
      channel.on<RawChatMessage>(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'threads',
          filter: `thread_id=eq.${currentThreadId}`,
        },
        (payload: RealtimePostgresChangesPayload<RawChatMessage>) => {
          // Extract the new message data
          const messageData = payload.new;
          if (!messageData) {
            console.warn('No message data in postgres_changes INSERT payload');
            return;
          }

          // Process the message directly
          handleRealtimeMessage(payload);
        }
      );

      // Also listen for UPDATE events (for intermediate steps)
      channel.on<RawChatMessage>(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'threads',
          filter: `thread_id=eq.${currentThreadId}`,
        },
        (payload: RealtimePostgresChangesPayload<RawChatMessage>) => {
          // Extract the updated message data
          const messageData = payload.new;
          if (!messageData) {
            console.warn('No message data in postgres_changes UPDATE payload');
            return;
          }

          // Process the message directly
          handleRealtimeMessage(payload);
        }
      );

      // Listen for DELETE events (for message deletion)
      channel.on<RawChatMessage>(
        'postgres_changes',
        {
          event: 'DELETE',
          schema: 'public',
          table: 'threads',
          filter: `thread_id=eq.${currentThreadId}`,
        },
        (payload: RealtimePostgresChangesPayload<RawChatMessage>) => {
          // Extract the deleted message data
          const deletedMessageData = payload.old as RawChatMessage;
          if (!deletedMessageData || Object.keys(deletedMessageData).length === 0) {
            console.warn('No message data in postgres_changes DELETE payload');
            return;
          }

          console.log(`🗑️ Message deleted: ${deletedMessageData.id}`);

          // Remove the deleted message(s) from the UI
          setMessages(prevMessages => {
            // If the deleted message has a message_id, remove all messages with the same message_id
            if (deletedMessageData.message_id) {
              // Convert both values to strings for safe comparison to avoid type mismatch
              const deletedMessageIdStr = String(deletedMessageData.message_id);
              const filteredMessages = prevMessages.filter(msg =>
                String(msg.message_id) !== deletedMessageIdStr
              );
              console.log(`Removed ${prevMessages.length - filteredMessages.length} messages with message_id ${deletedMessageData.message_id}`);
              return filteredMessages;
            } else {
              // Remove just the specific message by ID
              // Convert both values to strings for safe comparison
              const deletedIdStr = String(deletedMessageData.id);
              const filteredMessages = prevMessages.filter(msg =>
                String(msg.id) !== deletedIdStr
              );
              console.log(`Removed message with ID ${deletedMessageData.id}`);
              return filteredMessages;
            }
          });
        }
      );

      channel.subscribe((status, err) => {
        if (status === 'SUBSCRIBED') {
          console.log(`Successfully subscribed to postgres_changes channel ${channelName}!`);
        } else if (status === 'CHANNEL_ERROR') {
          console.error(`Channel error for ${channelName}:`, err);
        } else if (status === 'TIMED_OUT') {
          console.error(`Channel timeout for ${channelName}:`, err);
        } else {
          console.log(`Postgres_changes subscription status for ${channelName}: ${status}`, err);
        }
      });
    }

    // Cleanup function
    return () => {
      if (channel) {
        const currentChannelId = currentThreadId;
        console.log(`Unsubscribing from channel thread-${currentChannelId}`);
        supabase.removeChannel(channel)
          .then(() => console.log(`Successfully unsubscribed from channel thread-${currentChannelId}`))
          .catch(err => console.error(`Error unsubscribing from channel thread-${currentChannelId}:`, err));
      }
    };
  }, [currentThreadId, stopProcessing, handleRealtimeMessage]); // Dependencies


  // --- Dynamic Doc Export Subscription Management ---
  const docExportChannelsRef = useRef<Map<string, RealtimeChannel>>(new Map());

  // Function to unsubscribe from a specific doc export
  const unsubscribeFromDocExport = useCallback((messageId: string) => {
    const channel = docExportChannelsRef.current.get(messageId);
    if (channel) {
      console.log(`Unsubscribing from doc export for message: ${messageId}`);
      supabase.removeChannel(channel)
        .then(() => {
          console.log(`Successfully unsubscribed from doc export for message ${messageId}`);
          docExportChannelsRef.current.delete(messageId);
        })
        .catch(err => console.error(`Error unsubscribing from doc export for message ${messageId}:`, err));
    }
  }, []);

  // Function to subscribe to a specific doc export
  const subscribeToDocExport = useCallback((messageId: string) => {
    if (!user?.id) {
      console.warn('Cannot subscribe to doc export: user not available');
      return;
    }

    // Check if already subscribed to this message
    if (docExportChannelsRef.current.has(messageId)) {
      console.log(`Already subscribed to doc export for message ${messageId}`);
      return;
    }

    console.log(`Subscribing to doc export for message: ${messageId}`);

    const channelName = `doc-export-${messageId}`;
    const channel = supabase
      .channel(channelName)
      .on<DocExportRealtimePayload>(
        'postgres_changes',
        {
          event: '*', // Listen to INSERT, UPDATE, DELETE
          schema: 'public',
          table: 'doc_exports',
          filter: `threads_row_id=eq.${messageId}`, // Filter by specific message ID
        },
        (payload: RealtimePostgresChangesPayload<DocExportRealtimePayload>) => {
          console.log(`Doc Export event for message ${messageId}:`, payload);
          const { eventType, new: newDocExport } = payload;

          let relevantDocExport: DocExportRealtimePayload | undefined;
          if (eventType === 'INSERT' || eventType === 'UPDATE') {
            relevantDocExport = newDocExport;
          } else if (eventType === 'DELETE') {
            return; // Handle deletion if necessary
          }

          if (relevantDocExport && relevantDocExport.threads_row_id) {
            const messageIdToUpdate = relevantDocExport.threads_row_id.toString();

            // Update the specific message in the messages array
            setMessages(prevMessages =>
              prevMessages.map(msg => {
                if (msg.id?.toString() === messageIdToUpdate) {
                  return {
                    ...msg,
                    doc_export_status: relevantDocExport.status,
                    doc_export_url: relevantDocExport.doc_url,
                  };
                }
                return msg;
              })
            );
            console.log(`Updated message ${messageIdToUpdate} with doc_export_status: ${relevantDocExport.status}`);

            // If export is completed or failed, unsubscribe
            if (relevantDocExport.status === 'success' || relevantDocExport.status === 'error') {
              unsubscribeFromDocExport(messageId);
            }
          }
        }
      )
      .subscribe((status, err) => {
        if (status === 'SUBSCRIBED') {
          console.log(`Successfully subscribed to doc export for message ${messageId}!`);
        } else {
          console.error(`Doc export subscription status for ${messageId}: ${status}`, err);
        }
      });

    // Store the channel reference
    docExportChannelsRef.current.set(messageId, channel);
  }, [user?.id, unsubscribeFromDocExport]);

  // Cleanup all doc export subscriptions on unmount
  useEffect(() => {
    // Capture the current ref value at the time the effect runs
    const currentChannels = docExportChannelsRef.current;

    return () => {
      // Use the captured value in cleanup to avoid stale closure issues
      currentChannels.forEach((channel, messageId) => {
        console.log(`Cleaning up doc export subscription for message: ${messageId}`);
        supabase.removeChannel(channel).catch(console.error);
      });
      currentChannels.clear();
    };
  }, []);


  // The value that will be supplied to consuming components - memoized to prevent unnecessary re-renders
  const contextValue: ChatContextProps = useMemo(() => ({
    currentThreadId,
    setCurrentThreadId,
    messages,
    selectedAgentId,
    setSelectedAgentId: handleSetSelectedAgentId, // Provide the custom setter function
    isProcessingMessage,
    isLoadingHistory,
    isContextPanelOpen,
    isAgentSelectionRequired,
    currentThreadTitle,
    isLoadingTitle,
    addMessage,
    clearMessages,
    startProcessing,
    stopProcessing,
    toggleContextPanel,
    startNewConversation,
    updateThreadTitle,
    // Doc Export Functions
    subscribeToDocExport,
    unsubscribeFromDocExport,
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }), [
    currentThreadId,
    messages,
    selectedAgentId,
    isProcessingMessage,
    isLoadingHistory,
    isContextPanelOpen,
    isAgentSelectionRequired,
    currentThreadTitle,
    isLoadingTitle,
    // handleSetSelectedAgentId is stable (useCallback with empty deps), no need to include
    addMessage,
    clearMessages,
    startProcessing,
    stopProcessing,
    toggleContextPanel,
    startNewConversation,
    updateThreadTitle,
    subscribeToDocExport,
    unsubscribeFromDocExport,
  ]);

  return (
    <ChatContext.Provider value={contextValue}>
      {children}
    </ChatContext.Provider>
  );
};

// export default ChatProvider; // Optional default export