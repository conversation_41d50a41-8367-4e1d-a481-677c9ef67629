import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../UI/table';
import { Button } from '../UI/button';
import { Badge } from '../UI/badge';
import { Checkbox } from '../UI/checkbox';
import {
  ChevronUp,
  ChevronDown,
} from 'lucide-react';
import { MejoraAgenteListItem, EstadoMejoraAgente } from '../../types/evaluaciones';

interface ImprovementsListViewProps {
  mejoras: MejoraAgenteListItem[];
  selectedIds: string[];
  onToggleSelection: (id: string) => void;
  onSelectAll: (checked: boolean) => void;
  onClearSelection: () => void;
  onViewDetails: (id: string) => void;
  onApply: (id: string) => void;
  onDiscard: (id: string) => void;
  isLoading?: boolean;
  // Sorting props
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  onSort?: (column: string) => void;
  // Grouping props
  groupBy?: 'workflow' | 'agente' | 'none';
  onGroupByChange?: (groupBy: 'workflow' | 'agente' | 'none') => void;
}

const getEstadoBadge = (estado: EstadoMejoraAgente) => {
  const variants = {
    [EstadoMejoraAgente.PENDIENTE_REVISION_HUMANA]: { variant: 'secondary' as const, label: 'Pendiente Revisión' },
    [EstadoMejoraAgente.APROBADO_PARA_APLICAR]: { variant: 'default' as const, label: 'Aprobado para Aplicar' },
    [EstadoMejoraAgente.DESCARTADO]: { variant: 'destructive' as const, label: 'Descartado' },
    [EstadoMejoraAgente.APLICADO]: { variant: 'outline' as const, label: 'Aplicado' },
  };

  const config = variants[estado] || { variant: 'secondary' as const, label: estado };
  return <Badge variant={config.variant}>{config.label}</Badge>;
};

const SortableHeader: React.FC<{
  children: React.ReactNode;
  column: string;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  onSort?: (column: string) => void;
}> = ({ children, column, sortBy, sortDirection, onSort }) => {
  const isSorted = sortBy === column;
  
  return (
    <TableHead 
      className={`cursor-pointer hover:bg-gray-50 ${isSorted ? 'bg-gray-100' : ''}`}
      onClick={() => onSort?.(column)}
    >
      <div className="flex items-center gap-1">
        {children}
        {isSorted && (
          sortDirection === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
        )}
      </div>
    </TableHead>
  );
};

export const ImprovementsListView: React.FC<ImprovementsListViewProps> = ({
  mejoras,
  selectedIds,
  onToggleSelection,
  onSelectAll,
  onClearSelection,
  onViewDetails,
  onApply: _onApply,
  onDiscard: _onDiscard,
  isLoading = false,
  sortBy,
  sortDirection,
  onSort,
  groupBy = 'none',
  onGroupByChange,
}) => {
  const allSelected = mejoras.length > 0 && selectedIds.length === mejoras.length;
  const someSelected = selectedIds.length > 0 && selectedIds.length < mejoras.length;

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="h-16 bg-gray-200 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  if (mejoras.length === 0) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No hay mejoras disponibles
        </h3>
        <p className="text-gray-500">
          Las mejoras aparecerán aquí cuando se generen desde las evaluaciones.
        </p>
      </div>
    );
  }

  // Group mejoras if groupBy is set
  const groupedMejoras = React.useMemo(() => {
    if (groupBy === 'none') {
      return { 'Todas las mejoras': mejoras };
    }

    const groups: Record<string, MejoraAgenteListItem[]> = {};
    mejoras.forEach(mejora => {
      const key = groupBy === 'workflow'
        ? mejora.n8n_workflow_id || 'Sin workflow'
        : mejora.nombre_agente_amigable || 'Sin agente';

      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(mejora);
    });

    return groups;
  }, [mejoras, groupBy]);

  return (
    <div className="space-y-6">
      {/* Selection Controls */}
      {selectedIds.length > 0 && (
        <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
          <span className="text-sm text-blue-700">
            {selectedIds.length} mejora{selectedIds.length !== 1 ? 's' : ''} seleccionada{selectedIds.length !== 1 ? 's' : ''}
          </span>
          <div className="flex gap-2">
            <Button size="sm" variant="outline" onClick={onClearSelection}>
              Limpiar selección
            </Button>
          </div>
        </div>
      )}

      {/* Grouping Controls */}
      {onGroupByChange && (
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-700">Agrupar por:</span>
          <div className="flex gap-1">
            <Button
              size="sm"
              variant={groupBy === 'none' ? 'default' : 'outline'}
              onClick={() => onGroupByChange('none')}
            >
              Ninguno
            </Button>
            <Button
              size="sm"
              variant={groupBy === 'workflow' ? 'default' : 'outline'}
              onClick={() => onGroupByChange('workflow')}
            >
              Workflow
            </Button>
            <Button
              size="sm"
              variant={groupBy === 'agente' ? 'default' : 'outline'}
              onClick={() => onGroupByChange('agente')}
            >
              Agente
            </Button>
          </div>
        </div>
      )}

      {/* Table Groups */}
      {Object.entries(groupedMejoras).map(([groupName, groupMejoras]) => (
        <div key={groupName} className="space-y-2">
          {groupBy !== 'none' && (
            <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
              {groupName} ({groupMejoras.length})
            </h3>
          )}
          
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={allSelected}
                      ref={(el) => {
                        if (el) el.indeterminate = someSelected;
                      }}
                      onCheckedChange={(checked) => onSelectAll(!!checked)}
                    />
                  </TableHead>
                  <SortableHeader column="workflow" sortBy={sortBy} sortDirection={sortDirection} onSort={onSort}>
                    Workflow
                  </SortableHeader>
                  <SortableHeader column="agente" sortBy={sortBy} sortDirection={sortDirection} onSort={onSort}>
                    Nombre del agente
                  </SortableHeader>
                  <SortableHeader column="estado" sortBy={sortBy} sortDirection={sortDirection} onSort={onSort}>
                    Estado
                  </SortableHeader>
                  <TableHead>Acción</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {groupMejoras.map((mejora) => (
                  <TableRow key={mejora.id} className="hover:bg-gray-50">
                    <TableCell>
                      <Checkbox
                        checked={selectedIds.includes(mejora.id)}
                        onCheckedChange={() => onToggleSelection(mejora.id)}
                      />
                    </TableCell>
                    
                    <TableCell>
                      <div className="font-medium">
                        {mejora.n8n_workflow_id || 'Sin workflow'}
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="font-medium">
                        {mejora.nombre_agente_amigable || 'Sin nombre'}
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      {getEstadoBadge(mejora.estado)}
                    </TableCell>

                    <TableCell>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onViewDetails(mejora.id)}
                      >
                        Ver detalles
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      ))}
    </div>
  );
};
